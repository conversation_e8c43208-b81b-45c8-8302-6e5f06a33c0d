# 2025-07-28 汇远轻媒-校对助手部署记录

## 迁移概述
将hyqm校对助手相关业务从旧环境(200.1.66.x)迁移到腾讯云服务器，采用Docker容器化部署。

## 原始环境架构
- **服务器**: ************ (NFS存储服务器)
- **容器**: ai_ji<PERSON><PERSON>i_proxy_master (Python Flask应用)
- **数据库**: ************ MySQL
  - hyrm_news (170MB)
  - hy_jiaodui (4.5GB)
- **Redis**: 200.1.66.209:30103
- **RabbitMQ**: ************:5672
- **代理配置**: 208服务器nginx配置jiaodui.nmzyb.cn

## 新环境部署架构
- **容器**: aijiaodui-proxy-prod
- **镜像**: zhao0829wang/python-flask-graypy-pika:3.8.5-v1
- **端口映射**: 127.0.0.1:5001 → 容器5001端口
- **网络**: 1panel-network
- **存储路径**: /mnt/datadisk0/volumns/python-flask/aijiaodui/
- **配置路径**: /mnt/datadisk0/apps/python-flask/aijiaodui/

## 迁移过程记录

### 1. 数据库迁移
```bash
# 导出hyrm_news数据库 (在208服务器)
mysqldump -h ************ -u root -p'TxkjDB2020#' --single-transaction --routines --triggers hyrm_news > /usr/share/nginx/html/transfer/hyrm_news.sql

# 导出hy_jiaodui数据库 (在208服务器)
mysqldump -h ************ -u root -p'TxkjDB2020#' --single-transaction --routines --triggers hy_jiaodui > /usr/share/nginx/html/transfer/hy_jiaodui.sql

# 腾讯云下载并导入
wget https://main.51zsqc.com/transfer/hyrm_news.sql
wget https://main.51zsqc.com/transfer/hy_jiaodui.sql

# 清理GTID并导入
sed '/-- GTID state at the end of the backup/,/[0-9a-f-]*:[0-9-]*.*;$/d' hyrm_news.sql > hyrm_news_clean.sql
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' hyrm_news < /tmp/hyrm_news_clean.sql
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' hy_jiaodui < /tmp/hy_jiaodui_clean.sql

# 创建业务用户
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' -e "
CREATE USER IF NOT EXISTS 'hyrm'@'%' IDENTIFIED BY 'm2i06EzQRTag38pz';
GRANT ALL PRIVILEGES ON hyrm_news.* TO 'hyrm'@'%';
GRANT ALL PRIVILEGES ON hy_jiaodui.* TO 'hyrm'@'%';
FLUSH PRIVILEGES;"
```

### 2. 应用文件迁移
```bash
# 在208服务器打包整个crawl_all/master目录
ssh root@************ 'cd /data/nfs_share/crawl_all && tar czf - master/' > /usr/share/nginx/html/transfer/crawl_all_master.tar.gz

# 腾讯云下载并解压
wget https://main.51zsqc.com/transfer/crawl_all_master.tar.gz
tar xzf crawl_all_master.tar.gz -C /mnt/datadisk0/volumns/python-flask/aijiaodui/workdir/
```

### 3. Docker镜像迁移
```bash
# 在208服务器导出镜像
ssh root@************ 'docker save zhao0829wang/python-flask-graypy-pika:3.8.5-v1 | gzip' > /usr/share/nginx/html/transfer/python-flask-graypy-pika.tar.gz

# 腾讯云导入镜像
wget https://main.51zsqc.com/transfer/python-flask-graypy-pika.tar.gz
docker load < python-flask-graypy-pika.tar.gz
```

### 4. 容器配置文件

#### docker-compose.yml
```yaml
services:
  aijiaodui-proxy:
    image: zhao0829wang/python-flask-graypy-pika:3.8.5-v1
    container_name: aijiaodui-proxy-prod
    restart: always
    working_dir: /crawl_all/aijiaodui/src
    command: python main_flask.py
    ports:
      - "127.0.0.1:5001:5001"
    volumes:
      - /mnt/datadisk0/volumns/python-flask/aijiaodui/workdir:/crawl_all
      - /mnt/datadisk0/volumns/python-flask/aijiaodui/logs:/crawl_all/aijiaodui/src/logs
    env_file:
      - .env
    networks:
      - 1panel-network

networks:
  1panel-network:
    external: true
```

#### .env环境配置
```env
ENV_NAME=prod
REDIS_HOST=redis-prod
REDIS_PORT=6379
MYSQL_HOST=mysql-prod
MYSQL_PORT=3306
MYSQL_USER=hyrm
MYSQL_PASSWORD=m2i06EzQRTag38pz
RABBITMQ_HOST=rabbitmq-prod
RABBITMQ_PORT=5672
```

## 文件修改记录

### 1. env.py 配置文件更新
**路径**: `/mnt/datadisk0/volumns/python-flask/aijiaodui/workdir/aijiaodui/src/env.py`

**修改内容**:
- 使用环境变量获取配置参数
- 更新数据库连接信息指向新环境
- 更新Redis和RabbitMQ连接信息

### 2. models_jiaodui.py 数据库模型文件
**路径**: `/mnt/datadisk0/volumns/python-flask/aijiaodui/workdir/aijiaodui/src/lib/models_jiaodui.py`

**修改内容**:
- 添加`from urllib.parse import quote_plus`导入
- 使用URL编码处理数据库密码中的特殊字符
- 修改数据库连接字符串构建方式

**修改前**:
```python
engine = create_engine(f'mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB_JIAODUI}')
```

**修改后**:
```python
password_encoded = quote_plus(MYSQL_PASSWORD)
engine = create_engine(f'mysql+pymysql://{MYSQL_USER}:{password_encoded}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB_JIAODUI}')
```

### 3. historical_correction.py 历史纠错模块
**路径**: `/mnt/datadisk0/volumns/python-flask/aijiaodui/workdir/aijiaodui/src/historical_correction.py`

**修改内容**:
- 更新数据库配置为环境变量模式
- 使用新环境的数据库连接信息

**修改前**:
```python
DB_CONFIG = {
    'host': '************',
    'user': 'hyrm',
    'password': 'm2i06EzQRTag38pz',
    'database': 'hyrm_news'
}
```

**修改后**:
```python
DB_CONFIG = {
    'host': os.getenv('MYSQL_HOST', 'mysql-prod'),
    'user': os.getenv('MYSQL_USER', 'hyrm'),
    'password': os.getenv('MYSQL_PASSWORD', 'm2i06EzQRTag38pz'),
    'database': 'hyrm_news'
}
```

### 4. models_no_flask.py 其他数据库模型文件
**路径**: 
- `/mnt/datadisk0/volumns/python-flask/aijiaodui/workdir/correct_by_ai/src/lib/models_no_flask.py`
- `/mnt/datadisk0/volumns/python-flask/aijiaodui/workdir/aijiaodui/src/lib/models_no_flask.py`

**修改内容**:
- 添加URL编码处理密码特殊字符

## 网络配置

### OpenResty配置
- **域名**: jiaodui.nmzyb.cn
- **路由规则**:
  - `/nmzyb/` → 转发到 `127.0.0.1:5001`
  - 其他请求 → 转发到 `***************:24102`

## 服务验证

### 1. 容器状态检查
```bash
docker ps | grep aijiaodui
# 输出: aijiaodui-proxy-prod Up 127.0.0.1:5001->5001/tcp
```

### 2. 服务功能测试
```bash
curl -s http://127.0.0.1:5001/nmzyb/
# 输出: 这是本地处理的 nmzyb 路径
```

### 3. 数据库连接验证
```bash
docker exec mysql-prod mysql -u hyrm -p'm2i06EzQRTag38pz' -e "SHOW TABLES FROM hyrm_news;"
docker exec mysql-prod mysql -u hyrm -p'm2i06EzQRTag38pz' -e "SHOW TABLES FROM hy_jiaodui;"
```

## 部署完成状态

### 服务运行状态
- ✅ Python Flask应用正常启动
- ✅ 数据库连接成功
- ✅ Redis连接配置完成
- ✅ RabbitMQ连接配置完成
- ✅ 端口映射正常工作
- ✅ 日志输出正常

### 存储结构
```
/mnt/datadisk0/
├── apps/python-flask/aijiaodui/
│   ├── docker-compose.yml
│   ├── .env
│   └── logs/
└── volumns/python-flask/aijiaodui/
    ├── workdir/
    │   ├── aijiaodui/
    │   ├── python_lib_hyxx/
    │   └── 其他依赖模块/
    └── logs/
```

### 数据库用户权限
- **用户**: hyrm@'%'
- **权限**: hyrm_news.* 和 hy_jiaodui.* 全部权限

## 迁移总结

### 成功要点
1. **完整环境迁移**: 包含所有python_lib_hyxx依赖模块
2. **数据库用户一致**: 使用原始环境的hyrm用户
3. **网络架构适配**: 端口映射解决host网络与容器网络通信问题
4. **特殊字符处理**: URL编码解决密码特殊字符问题

### 技术难点解决
1. **模块依赖**: 迁移完整crawl_all/master目录解决python_lib_hyxx导入问题
2. **数据库认证**: 创建原始hyrm用户而非使用pearproject用户
3. **网络通信**: 1Panel OpenResty使用host网络，需要端口映射到宿主机
4. **密码编码**: 特殊字符需要URL编码处理

### 迁移时间
- **数据库迁移**: ~30分钟 (包含4.5GB数据传输)
- **应用部署**: ~20分钟
- **问题调试**: ~40分钟
- **总计**: ~90分钟

## 后续维护

### 监控要点
- 容器运行状态: `docker ps | grep aijiaodui`
- 服务响应: `curl http://127.0.0.1:5001/nmzyb/`
- 数据库连接: 监控应用日志
- 磁盘空间: 注意日志文件增长

### 备份策略
- 应用代码: /mnt/datadisk0/volumns/python-flask/aijiaodui/workdir/
- 配置文件: /mnt/datadisk0/apps/python-flask/aijiaodui/
- 数据库: 定期mysqldump导出

**迁移完成时间**: 2025-07-28 16:45
**负责人**: Claude AI Assistant
**状态**: ✅ 部署成功，服务正常运行