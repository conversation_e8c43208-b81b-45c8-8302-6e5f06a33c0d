# ICBC相关业务运维手册

## 项目概览

**项目名称**: ICBC相关业务（淖尔e钱包、交通罚没、运远直销）  
**技术架构**: PHP 7.2 + Apache + MySQL + Redis + JavaBridge  
**服务器环境**: Ubuntu 24.04 + Docker容器化部署  
**域名组**: naoer.icbc.51zsqc.com, jtgf.icbc.51zsqc.com, zjgx.icbc.51zsqc.com

## 服务架构

```
外部访问 → 雷池WAF → 1Panel OpenResty → PHP Web容器 + JavaBridge容器
                                      ↓
                        MySQL + Redis 容器
```

### 核心容器服务

| 容器名称 | 镜像 | 端口映射 | 用途 |
|---------|------|----------|------|
| icbc-naoer-web | zhao0829wang/apache2-php:7.2-rc4 | 20031:80 | 淖尔e钱包Web服务 |
| icbc-naoer-bridge | openjdk:11-jdk | 31802:22222 | 淖尔e钱包Java桥接服务 |
| icbc-jtgf-web | zhao0829wang/apache2-php:7.2-rc4 | 20032:80 | 交通罚没Web服务 |
| icbc-jtgf-bridge | openjdk:11-jdk | 31803:22222 | 交通罚没Java桥接服务 |
| icbc-zjgx-web | zhao0829wang/apache2-php:7.2-rc4 | 20033:80 | 运远直销Web服务 |
| icbc-zjgx-bridge | openjdk:11-jdk | 31804:22222 | 运远直销Java桥接服务 |
| mysql-prod | mysql:5.7.44 | 3306 | 数据库服务 |
| redis-prod | redis:8.0.3 | 6379 | 缓存服务 |

## 目录结构

```
/mnt/datadisk0/
├── apps/icbc/                    # ICBC应用配置
│   ├── naoer/
│   │   ├── docker-compose.yml        # 淖尔e钱包容器配置
│   │   └── .env                       # 环境变量
│   ├── jtgf/
│   │   ├── docker-compose.yml        # 交通罚没容器配置
│   │   └── .env                       # 环境变量
│   └── zjgx/
│       ├── docker-compose.yml        # 运远直销容器配置
│       └── .env                       # 环境变量
├── volumns/icbc/                 # ICBC应用数据
│   ├── naoer/
│   │   ├── web/                      # Web应用代码
│   │   └── bridge/                   # Java桥接程序
│   ├── jtgf/
│   │   ├── web/                      # Web应用代码
│   │   └── bridge/                   # Java桥接程序
│   └── zjgx/
│       ├── web/                      # Web应用代码
│       └── bridge/                   # Java桥接程序
└── backups/icbc/                 # 备份文件
```


## 定时任务
```
sudo vi /crontab/crontab
crontab /crontab/crontab
crontab -l
```


```
# -----  光伏惠民e钱包   -----
#每10分钟进行一次 SFTP文件服务器检测
*/10 * * * * curl http://cf.icbc.51zsqc.com/index.php/icbc/crontab/checkSftp
# --- 每天早上4点，创建一个同步用户余额的任务
00 4 * * * curl http://cf.icbc.51zsqc.com/index.php/icbc/crontab/createTask
# --- 每3天早上9点创建代扣任务
00 9 */3 * * curl http://cf.icbc.51zsqc.com/index.php/icbc/crontab/createDaiKou
#每分钟执行一次用户余额同步任务
*/1 * * * * curl http://cf.icbc.51zsqc.com/index.php/icbc/crontab/syncAmount


# -----  久泰e钱包   -----
*/10 * * * * curl http://jtgf.icbc.51zsqc.com/index.php/icbc/crontab/checkSftp
00 4 * * * curl http://jcgf.icbc.51zsqc.com/index.php/icbc/crontab/createTask
00 9 */3 * * curl http://cf.icbc.51zsqc.com/index.php/icbc/crontab/createDaiKou
*/1 * * * * curl http://jtgf.icbc.51zsqc.com/index.php/icbc/crontab/syncAmount

# -----  光伏创收e钱包   -----
#每10分钟进行一次 SFTP文件服务器检测
*/10 * * * * curl http://zjgx.icbc.51zsqc.com/index.php/icbc/crontab/checkSftp
# --- 每天早上4点，创建一个同步用户余额的任务
00 4 * * * curl http://zjgx.icbc.51zsqc.com/index.php/icbc/crontab/createTask
#每分钟执行一次用户余额同步任务
*/1 * * * * curl http://zjgx.icbc.51zsqc.com/index.php/icbc/crontab/syncAmount



# -----  光优惠民e钱包   -----
#每10分钟进行一次 SFTP文件服务器检测
*/10 * * * * curl http://naoer.icbc.51zsqc.com/index.php/icbc/crontab/checkSftp
# --- 每天早上4点，创建一个同步用户余额的任务
00 4 * * * curl http://naoer.icbc.51zsqc.com/index.php/icbc/crontab/createTask
#每分钟执行一次用户余额同步任务
*/1 * * * * curl http://naoer.icbc.51zsqc.com/index.php/icbc/crontab/syncAmount
# --- 每天早上9点创建代扣任务
00 9 * * * curl http://naoer.icbc.51zsqc.com/index.php/icbc/crontab/createDaiKou
# --- 生成导出用户文件
* * * * * curl http://naoer.icbc.51zsqc.com/index.php/icbc/crontab/exportUser
# --- 删除已经下载的导出用户文件
* * * * * curl http://naoer.icbc.51zsqc.com/index.php/icbc/crontab/deleteExportFile

```

## 数据库信息

### 主要数据库

| 数据库名 | 业务 | 大小 | 用途 |
|---------|------|------|------|
| icbc_naoer | 淖尔e钱包 | ~500MB | 钱包业务数据 |
| icbc_naoer_log | 淖尔e钱包 | ~200MB | 操作日志 |
| icbc_jtgf | 交通罚没 | ~800MB | 罚没业务数据 |
| icbc_zjgx | 运远直销 | ~300MB | 直销业务数据 |

### 数据库连接信息
- **主机**: mysql-prod (容器内网络)
- **用户**: pearproject  
- **密码**: kG7#tPq9@zR2$vX1
- **端口**: 3306

## 日常维护操作

### 1. 服务状态检查

```bash
# 检查所有ICBC容器状态
docker ps | grep icbc

# 检查容器资源使用
docker stats --no-stream | grep icbc

# 检查服务端口
netstat -tlnp | grep -E "(20031|20032|20033|31802|31803|31804)"
```

### 2. 应用日志查看

#### 容器应用日志
```bash
# 淖尔e钱包Web日志
docker logs icbc-naoer-web --tail 100
docker exec icbc-naoer-web tail -f /var/log/apache2/error.log

# 淖尔e钱包Java桥接日志
docker logs icbc-naoer-bridge --tail 100

# 交通罚没Web日志
docker logs icbc-jtgf-web --tail 100
docker exec icbc-jtgf-web tail -f /var/log/apache2/error.log

# 运远直销Web日志
docker logs icbc-zjgx-web --tail 100
docker exec icbc-zjgx-web tail -f /var/log/apache2/error.log
```

#### 1Panel网站访问日志
```bash
# 淖尔e钱包访问日志
tail -f /opt/1panel/www/sites/naoer.icbc.51zsqc.com/log/access.log
tail -f /opt/1panel/www/sites/naoer.icbc.51zsqc.com/log/error.log

# 交通罚没访问日志
tail -f /opt/1panel/www/sites/jtgf.icbc.51zsqc.com/log/access.log
tail -f /opt/1panel/www/sites/jtgf.icbc.51zsqc.com/log/error.log

# 运远直销访问日志
tail -f /opt/1panel/www/sites/zjgx.icbc.51zsqc.com/log/access.log
tail -f /opt/1panel/www/sites/zjgx.icbc.51zsqc.com/log/error.log

# 统计访问量
grep "$(date '+%d/%b/%Y')" /opt/1panel/www/sites/naoer.icbc.51zsqc.com/log/access.log | wc -l
```

### 3. 数据库维护

```bash
# 连接到对应业务数据库
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' icbc_naoer
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' icbc_jtgf
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' icbc_zjgx

# 查看数据库大小
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' -e "
SELECT 
    table_schema as 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema IN ('icbc_naoer', 'icbc_naoer_log', 'icbc_jtgf', 'icbc_zjgx')
GROUP BY table_schema;"

# 备份ICBC相关数据库
docker exec mysql-prod mysqldump -u root -p'56d9DavJ*zwrwj9rmA' --single-transaction icbc_naoer > /tmp/icbc_naoer_$(date +%Y%m%d).sql
docker exec mysql-prod mysqldump -u root -p'56d9DavJ*zwrwj9rmA' --single-transaction icbc_jtgf > /tmp/icbc_jtgf_$(date +%Y%m%d).sql
docker exec mysql-prod mysqldump -u root -p'56d9DavJ*zwrwj9rmA' --single-transaction icbc_zjgx > /tmp/icbc_zjgx_$(date +%Y%m%d).sql
```

### 4. 缓存管理

```bash
# Redis缓存状态
docker exec redis-prod redis-cli INFO memory

# 查看ICBC相关缓存键
docker exec redis-prod redis-cli KEYS "*icbc*"
docker exec redis-prod redis-cli KEYS "*naoer*"
docker exec redis-prod redis-cli KEYS "*jtgf*"
docker exec redis-prod redis-cli KEYS "*zjgx*"

# 清理特定业务缓存
docker exec redis-prod redis-cli DEL $(docker exec redis-prod redis-cli KEYS "*icbc*")
```

### 5. JavaBridge服务管理

```bash
# 检查JavaBridge进程状态
docker exec icbc-naoer-bridge ps aux | grep java
docker exec icbc-jtgf-bridge ps aux | grep java
docker exec icbc-zjgx-bridge ps aux | grep java

# 重启JavaBridge服务
docker restart icbc-naoer-bridge icbc-jtgf-bridge icbc-zjgx-bridge

# 测试JavaBridge连接
curl -I http://127.0.0.1:31802
curl -I http://127.0.0.1:31803
curl -I http://127.0.0.1:31804
```

### 6. 代码更新部署

```bash
# 进入代码目录
cd /mnt/datadisk0/volumns/icbc

# 更新淖尔e钱包代码
cd naoer/web && git pull origin master

# 更新交通罚没代码
cd ../../jtgf/web && git pull origin master

# 更新运远直销代码
cd ../../zjgx/web && git pull origin master

# 重启相关容器
docker restart icbc-naoer-web icbc-jtgf-web icbc-zjgx-web
```

## 业务特定操作

### 淖尔e钱包 (naoer.icbc.51zsqc.com)

```bash
# 检查钱包余额同步状态
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' icbc_naoer -e "SELECT COUNT(*) FROM wallet_balance WHERE updated_at > DATE_SUB(NOW(), INTERVAL 1 HOUR);"

# 查看最近交易记录
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' icbc_naoer -e "SELECT * FROM transactions ORDER BY created_at DESC LIMIT 10;"
```

### 交通罚没 (jtgf.icbc.51zsqc.com)

```bash
# 检查罚款缴费状态
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' icbc_jtgf -e "SELECT status, COUNT(*) FROM fine_payments WHERE created_at > CURDATE() GROUP BY status;"

# 查看最近处理的罚单
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' icbc_jtgf -e "SELECT * FROM traffic_fines ORDER BY processed_at DESC LIMIT 10;"
```

### 运远直销 (zjgx.icbc.51zsqc.com)

```bash
# 检查直销订单状态
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' icbc_zjgx -e "SELECT status, COUNT(*) FROM orders WHERE created_at > CURDATE() GROUP BY status;"

# 查看最近订单
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' icbc_zjgx -e "SELECT * FROM orders ORDER BY created_at DESC LIMIT 10;"
```

## 故障排查

### 常见问题

1. **JavaBridge连接失败**
   ```bash
   # 检查Java进程
   docker exec icbc-naoer-bridge jps
   
   # 重启桥接服务
   docker restart icbc-naoer-bridge
   
   # 检查端口监听
   docker exec icbc-naoer-bridge netstat -tlnp | grep 22222
   ```

2. **数据库连接超时**
   ```bash
   # 检查数据库连接数
   docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' -e "SHOW PROCESSLIST;"
   
   # 重启MySQL容器
   docker restart mysql-prod
   ```

3. **缓存数据异常**
   ```bash
   # 清理Redis缓存
   docker exec redis-prod redis-cli FLUSHDB
   
   # 重启Redis服务
   docker restart redis-prod
   ```

## 监控指标

### 关键指标

1. **容器健康状态**: 所有ICBC容器Running状态
2. **响应时间**: Web服务响应时间 < 3秒
3. **JavaBridge可用性**: 端口22222可访问
4. **数据库连接**: MySQL连接数 < 100
5. **Redis内存使用**: < 1GB

### 监控命令

```bash
# 容器状态监控
docker ps | grep icbc | wc -l

# 服务响应监控
curl -w "%{time_total}" -o /dev/null -s http://naoer.icbc.51zsqc.com/health

# 资源使用监控
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | grep icbc
```

## 备份策略

### 数据备份
- **数据库**: 每日凌晨2点自动备份
- **代码**: Git仓库备份
- **配置文件**: 每周备份到NFS存储

### 备份脚本
```bash
#!/bin/bash
# ICBC业务备份脚本
DATE=$(date +%Y%m%d)
BACKUP_DIR="/mnt/datadisk0/backups/icbc/$DATE"
mkdir -p $BACKUP_DIR

# 数据库备份
docker exec mysql-prod mysqldump -u root -p'56d9DavJ*zwrwj9rmA' --single-transaction icbc_naoer > $BACKUP_DIR/icbc_naoer.sql
docker exec mysql-prod mysqldump -u root -p'56d9DavJ*zwrwj9rmA' --single-transaction icbc_jtgf > $BACKUP_DIR/icbc_jtgf.sql
docker exec mysql-prod mysqldump -u root -p'56d9DavJ*zwrwj9rmA' --single-transaction icbc_zjgx > $BACKUP_DIR/icbc_zjgx.sql

# 配置备份
cp -r /mnt/datadisk0/apps/icbc $BACKUP_DIR/

echo "ICBC backup completed: $BACKUP_DIR"
```

## 更新记录

| 日期 | 版本 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 2025-07-29 | v1.0 | 初始版本创建 | Claude |

---
**维护负责人**: 运维团队  
**紧急联系**: 技术支持部门