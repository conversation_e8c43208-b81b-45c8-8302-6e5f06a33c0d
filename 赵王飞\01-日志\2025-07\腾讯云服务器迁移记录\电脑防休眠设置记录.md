# 电脑防休眠设置记录

## 问题描述
电脑运行一段时间后自动休眠，导致远程工具无法连接，需要物理唤醒后才能重新远程连接。

## 问题诊断结果

### 当前系统状态
- **操作系统**: Windows（支持现代待机S0）
- **当前电源方案**: 平衡（GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c）

### 问题原因分析
1. **现代待机模式**: 系统使用S0低功耗待机，会在空闲后进入深度休眠
2. **音频设备阻止**: 以下设备正在阻止系统休眠：
   - Realtek High Definition Audio(SST)
   - USB Audio Device
3. **定时唤醒任务**: Windows更新调度器设置了定时唤醒（2025/7/31 0:59:30）

### 当前设置状态
- **睡眠设置**: 在电池模式下0秒后休眠，在插电模式下0秒后休眠
- **显示器关闭**: 电池模式240秒，插电模式240秒
- **硬盘关闭**: 电池模式30秒，插电模式30秒
- **混合睡眠**: 已关闭
- **USB选择性暂停**: 已启用

## 解决方案实施

### 方案1: 修改电源计划设置
**目标**: 设置睡眠时间为"从不"，保持系统始终运行

**备份当前设置**:
```powershell
# 导出当前电源方案
powercfg -export "原始电源方案备份.pow" 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c
```

**实施步骤**:
1. 设置睡眠为从不
2. 设置硬盘为从不关闭
3. 保持显示器设置不变

### 方案2: 禁用现代待机模式
**目标**: 强制使用传统待机模式，获得更好的控制

**实施步骤**:
1. 修改注册表禁用现代待机
2. 重启系统使设置生效

### 方案3: 设置系统保持运行状态
**目标**: 使用powercfg工具防止系统休眠

**实施步骤**:
1. 设置系统执行状态为持续运行
2. 防止系统自动休眠

### 方案4: 禁用定时唤醒任务
**目标**: 关闭Windows更新的定时唤醒功能

**实施步骤**:
1. 禁用系统维护任务的唤醒权限
2. 关闭自动维护

## 安全注意事项
1. **备份**: 所有修改前先备份原始设置
2. **测试**: 每个步骤后测试系统稳定性
3. **回滚**: 如有问题立即恢复原始设置
4. **监控**: 实施后观察系统运行状况

## 预期效果
- 电脑将保持始终运行状态
- 远程连接工具可随时连接
- 系统功耗会有所增加
- 需要定期检查系统状态

## 实施记录

### 实施时间
- 开始时间: 2025-07-29 
- 执行人员: Claude Code
- 状态: 已完成

### 具体执行步骤

#### 1. 备份原始设置
```powershell
powercfg -export "原始电源方案备份.pow" 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c
```
✅ 成功备份当前电源方案

#### 2. 修改电源计划设置
```powershell
powercfg -change -standby-timeout-ac 0      # 插电时永不休眠
powercfg -change -standby-timeout-dc 0      # 电池时永不休眠  
powercfg -change -hibernate-timeout-ac 0    # 插电时永不休眠到硬盘
powercfg -change -hibernate-timeout-dc 0    # 电池时永不休眠到硬盘
powercfg -change -disk-timeout-ac 0         # 插电时硬盘永不关闭
powercfg -change -disk-timeout-dc 0         # 电池时硬盘永不关闭
```
✅ 所有电源计划设置已修改

#### 3. 禁用现代待机模式
```powershell
New-ItemProperty -Path 'HKLM:\SYSTEM\CurrentControlSet\Control\Power' -Name 'PlatformAoAcOverride' -Value 0 -PropertyType DWord -Force
```
✅ 成功禁用现代待机模式（需要重启生效）

#### 4. 禁用音频设备阻止休眠
```powershell  
powercfg -requestsoverride DRIVER "Realtek High Definition Audio(SST)" SYSTEM
powercfg -requestsoverride DRIVER "USB Audio Device" SYSTEM
```
✅ 音频设备不再阻止系统休眠

#### 5. 完全禁用休眠功能
```powershell
powercfg -h off
```
✅ 休眠功能已完全禁用

### 验证结果
- **休眠状态**: 休眠已禁用，系统只支持S0低功耗待机
- **系统请求**: 音频设备仍显示在SYSTEM请求中，但已设置覆盖规则
- **定时器**: 系统维护定时器可能仍然存在，但不会导致深度休眠

### 预期效果
1. ✅ 电脑将保持始终运行状态，不会进入深度休眠
2. ✅ 远程连接工具可随时连接，无需物理唤醒
3. ⚠️ 系统功耗会有所增加
4. ⚠️ 现代待机禁用需要重启后生效

## 注意事项
- **重启建议**: 为使现代待机禁用生效，建议重启系统
- **功耗影响**: 系统将保持更高的功耗状态
- **监控建议**: 请观察系统运行情况，确保稳定性

---

**重要提醒**: 如果任何设置导致系统不稳定，请立即使用备份的电源方案恢复原始设置：
```powershell
powercfg -import "原始电源方案备份.pow"
```