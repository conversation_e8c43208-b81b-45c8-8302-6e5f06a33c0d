# hyrm_imusti (监察官学院) 迁移计划

## 1. 项目基本信息

### 1.1 项目概述
- **项目名称**：hyrm_imusti (监察官学院)
- **项目类型**：Java Web应用 (Spring Boot + Vue)
- **当前环境**：K8s集群 + NFS共享存储
- **目标环境**：Docker容器 + 本地卷挂载
- **迁移优先级**：中等 (数据库适中，风险可控)

### 1.2 技术架构
```
前端：Vue.js (Nginx静态文件服务)
后端：Spring Boot (Java应用)
数据库：MySQL (独立数据库)
缓存：Redis (共享)
消息队列：RabbitMQ (共享)
```

## 2. 迁移范围分析

### 2.1 数据库依赖
- **hyrm_imusti** - 监察官学院独立数据库 **237MB** ✅
- **hyrm_news** - 消息模块数据库 (已迁移完成 ✅)

### 2.2 文件存储分析
**NFS存储路径**：`/data/nfs_share/html/HOME_RUOYI_PROD/hyrm_imusti/` **270MB**
```
hyrm_imusti/
├── java/          # 后端JAR文件和配置
├── logs/          # 应用日志文件
└── vue/           # 前端静态文件
    ├── dist/      # 构建后的前端文件
    ├── static/    # 静态资源
    └── uploads/   # 用户上传文件
```

### 2.3 存储空间评估
- **源数据库大小**：237MB (hyrm_imusti)
- **应用文件大小**：270MB
- **目标服务器可用空间**：427GB ✅
- **预计传输时间**：数据库约3分钟，文件约5分钟

### 2.4 容器镜像信息
根据汇远融媒标准架构，预计使用镜像：`4525e4716f8b` (汇远融媒标准镜像)

## 3. 迁移前准备工作

### 3.1 环境检查
- [ ] 确认208服务器mysqldump工具可用
- [ ] 确认腾讯云Docker环境正常运行
- [ ] 确认mysql-prod容器状态正常
- [ ] 确认1panel-network网络连通性
- [ ] 确认文件传输域名可访问

### 3.2 数据收集
- [ ] 确认hyrm_imusti数据库表结构
- [ ] 确认应用文件目录结构
- [ ] 确认当前K8s容器配置参数
- [ ] 确认端口和域名配置需求
- [ ] 确认依赖的外部服务

### 3.3 风险评估
- **数据风险**：数据库237MB，传输时间短，风险低 ✅
- **服务风险**：监察官学院服务，用户相对较少
- **配置风险**：数据库连接配置需要更新
- **依赖风险**：确保Redis、RabbitMQ等服务可用
- **存储风险**：文件较小，网络传输风险低 ✅

## 4. 详细迁移计划

### 4.1 阶段一：数据库迁移 (预计5分钟)

#### 步骤1：导出hyrm_imusti数据库
```bash
# 在208服务器执行
mysqldump -h 200.1.66.214 -u root -p'TxkjDB2020#' \
  --single-transaction --routines --triggers hyrm_imusti > /tmp/hyrm_imusti.sql

# 检查文件大小
ls -lh /tmp/hyrm_imusti.sql
```

#### 步骤2：压缩并传输
```bash
# 压缩文件
tar -czf /usr/share/nginx/html/transfer/hyrm_imusti.sql.tar.gz /tmp/hyrm_imusti.sql

# 腾讯云下载
wget -O /tmp/hyrm_imusti.sql.tar.gz https://main.51zsqc.com/transfer/hyrm_imusti.sql.tar.gz
```

#### 步骤3：数据库导入
```bash
# 解压并复制到容器
cd /tmp && tar -xzf hyrm_imusti.sql.tar.gz
docker cp /tmp/hyrm_imusti.sql mysql-prod:/tmp/

# 创建数据库
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' \
  -e "CREATE DATABASE IF NOT EXISTS hyrm_imusti DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 处理GTID并导入
docker exec mysql-prod bash -c "sed '/-- GTID state at the end of the backup/,/[0-9]*.*;$/d' /tmp/hyrm_imusti.sql > /tmp/hyrm_imusti_clean.sql"
docker exec mysql-prod bash -c "mysql -u root -p'56d9DavJ*zwrwj9rmA' hyrm_imusti < /tmp/hyrm_imusti_clean.sql"

# 配置权限
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' \
  -e "GRANT ALL PRIVILEGES ON hyrm_imusti.* TO 'pearproject'@'%'; FLUSH PRIVILEGES;"
```

### 4.2 阶段二：应用文件迁移 (预计10分钟)

#### 步骤1：打包应用文件（通过208远程执行）
```bash
# 在208服务器上执行远程打包
ssh root@************ "cd /data/nfs_share/html/HOME_RUOYI_PROD/ && tar -czf - hyrm_imusti/" > /usr/share/nginx/html/transfer/hyrm_imusti_files_$(date +%Y%m%d_%H%M%S).tar.gz

# 检查文件
ls -lh /usr/share/nginx/html/transfer/hyrm_imusti_files_*.tar.gz
```

#### 步骤2：下载并部署文件
```bash
# 腾讯云下载
wget -O /tmp/hyrm_imusti_files.tar.gz https://main.51zsqc.com/transfer/hyrm_imusti_files_[时间戳].tar.gz

# 创建目录结构
mkdir -p /mnt/datadisk0/apps/java-web/hyrm-imusti/{conf,logs,scripts}
mkdir -p /mnt/datadisk0/volumns/java-web/hyrm-imusti/{workdir,ui,logs}

# 解压应用文件
tar -xzf /tmp/hyrm_imusti_files.tar.gz -C /tmp/
cp -r /tmp/hyrm_imusti/java/* /mnt/datadisk0/volumns/java-web/hyrm-imusti/workdir/
cp -r /tmp/hyrm_imusti/vue/* /mnt/datadisk0/volumns/java-web/hyrm-imusti/ui/
cp -r /tmp/hyrm_imusti/logs/* /mnt/datadisk0/volumns/java-web/hyrm-imusti/logs/
```

### 4.3 阶段三：容器配置和部署 (预计10分钟)

#### 步骤1：检查配置
```bash
# 检查可用的profile
cd /mnt/datadisk0/volumns/java-web/hyrm-imusti/workdir/
unzip -l ruoyi-admin.jar | grep "application-.*\.yml" | head -10
```

#### 步骤2：创建Docker Compose配置
```yaml
# /mnt/datadisk0/apps/java-web/hyrm-imusti/docker-compose.yml
version: '3.8'

networks:
  1panel-network:
    external: true

services:
  hyrm-imusti:
    image: 4525e4716f8b
    container_name: hyrm-imusti
    networks:
      - 1panel-network
    volumes:
      - /mnt/datadisk0/volumns/java-web/hyrm-imusti/workdir:/workdir
      - /mnt/datadisk0/volumns/java-web/hyrm-imusti/logs:/home/<USER>/logs
      - /mnt/datadisk0/volumns/java-web/hyrm-imusti/ui:/home/<USER>/projects/ruoyi-ui
      - /etc/localtime:/etc/localtime:ro
    environment:
      - TZ=Asia/Shanghai
      - COMMAND_NAME=-jar ruoyi-admin.jar --spring.profiles.active=prod --ruoyi.profile=/workdir/upload --spring.redis.host=redis-prod --spring.redis.port=6379 --spring.datasource.druid.master.url=*****************************************************************************************************************************************************
    restart: unless-stopped
    ports:
      - "8085:80"  # 避免与其他服务端口冲突
```

#### 步骤3：启动服务
```bash
cd /mnt/datadisk0/apps/java-web/hyrm-imusti/
docker compose up -d

# 监控启动日志
docker logs -f hyrm-imusti
```

### 4.4 阶段四：验证和清理 (预计5分钟)

#### 步骤1：功能验证
```bash
# 等待应用启动
sleep 30

# 检查容器状态
docker ps | grep hyrm-imusti

# 查看启动日志
docker logs hyrm-imusti --tail 100 | grep -E "(Started|Error|Exception)"

# API接口测试
curl -I http://localhost:8085/

# 数据库连接测试
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' \
  -e "USE hyrm_imusti; SHOW TABLES;" | wc -l
```

#### 步骤2：清理临时文件
```bash
# 先确认服务正常后再清理！

# 208服务器清理
rm -f /usr/share/nginx/html/transfer/hyrm_imusti*.gz
rm -f /tmp/hyrm_imusti*.sql

# 腾讯云服务器清理
rm -f /tmp/hyrm_imusti*
rm -rf /tmp/hyrm_imusti/

# 容器内清理
docker exec mysql-prod rm -f /tmp/hyrm_imusti*.sql
```

## 5. 验收标准

### 5.1 数据库验收
- [ ] hyrm_imusti数据库成功创建
- [ ] 所有表结构完整导入
- [ ] 数据记录数量一致
- [ ] pearproject用户权限正确

### 5.2 应用验收
- [ ] 容器成功启动
- [ ] 应用日志无错误
- [ ] API接口响应正常
- [ ] 前端页面可访问
- [ ] 数据库连接正常

### 5.3 功能验收
- [ ] 用户登录功能正常
- [ ] 数据查询功能正常
- [ ] 文件上传下载功能正常
- [ ] 页面跳转功能正常

## 6. 回滚方案

### 6.1 回滚触发条件
- 数据库导入失败
- 应用启动失败
- 功能验证失败
- 性能严重下降

### 6.2 回滚步骤
1. 停止新容器：`docker compose down`
2. 删除新数据库：`DROP DATABASE hyrm_imusti;`
3. 恢复K8s服务访问
4. 清理迁移文件
5. 记录问题和原因

## 7. 时间安排

### 7.1 预计总时间：30分钟
- 数据库迁移：5分钟 (237MB)
- 文件迁移：10分钟 (270MB)
- 容器部署：10分钟
- 验证清理：5分钟

### 7.2 建议执行时间
- 任何时间都可以执行 (数据量小，风险低)
- 建议在工作时间执行，便于及时处理问题
- 预留1小时时间窗口 (包含问题处理时间)

## 8. 注意事项

### 8.1 关键配置检查
- 数据库连接字符串 (hyrm_imusti)
- Redis连接配置
- RabbitMQ队列配置
- 文件上传路径配置

### 8.2 依赖服务确认
- mysql-prod容器运行正常
- redis-prod容器运行正常
- rabbitmq-prod容器运行正常
- 1panel-network网络正常

### 8.3 监控要点
- 容器启动日志
- 数据库连接状态
- API接口响应
- 前端页面加载

## 9. 迁移优势

### 9.1 相比hyqm_prod的优势
- ✅ **数据库小**：237MB vs 26.3GB
- ✅ **传输快**：5分钟 vs 90分钟
- ✅ **风险低**：用户少，影响范围小
- ✅ **回滚快**：30分钟内可完成回滚

### 9.2 作为练习项目的价值
- 验证标准化流程的有效性
- 积累小型项目迁移经验
- 为大型项目迁移做准备
- 测试容器网络和配置

这个迁移计划基于标准化操作手册制定，hyrm_imusti作为中小型项目，是验证迁移流程的理想选择。
