# ICBC光伏项目规范文档

## 1. 项目概述

ICBC光伏项目包含4个子项目，均为工商银行光伏惠民e钱包系统的不同地区实施：

| 项目代码 | 项目名称 | 域名 | 端口 | 状态 |
|---------|---------|------|------|------|
| naoer | 光优惠民e钱包 | naoer.icbc.51zsqc.com | 20031 | ✅ 运行中 |
| chifeng | 光伏惠民e钱包 | cf.icbc.51zsqc.com | 20032 | ✅ 运行中 |
| jtgf | 光伏项目 | jtgf.icbc.51zsqc.com | 20034 | ✅ 运行中 |
| zjgx | 光伏创收e钱包 | zjgx.icbc.51zsqc.com | 20033 | ✅ 运行中 |

## 2. 架构设计

### 2.1 技术栈
- **前端**: PHP 7.2 + Apache 2.4
- **后端**: Java Bridge (JavaBridge.jar)
- **数据库**: MySQL (mysql-prod)
- **缓存**: Redis (redis-prod)
- **部署**: Docker + docker-compose

### 2.2 目录结构
```
/mnt/datadisk0/
├── apps/                           # 应用配置管理
│   ├── icbc-naoer/
│   │   ├── conf/apache2/default.conf
│   │   ├── logs/
│   │   ├── docker-compose.yml
│   │   └── manage.sh
│   ├── icbc-chifeng/
│   ├── icbc-jtgf/ (待迁移)
│   └── icbc-zjgx/ (待迁移)
└── volumns/                        # 数据存储
    └── icbc/
        ├── naoer/
        │   ├── web/                # PHP应用代码
        │   └── bridge/             # Java Bridge应用
        ├── chifeng/
        ├── jtgf/
        └── zjgx/
```

### 2.3 网络架构
每个项目使用独立的Docker网络：
- icbc-naoer-network (**********/24)
- icbc-chifeng-network (**********/24)
- jtgf和zjgx使用默认网络

## 3. 端口配置规范

### 3.1 Web服务端口
- naoer: 20031:80
- chifeng: 20032:80
- jtgf: 20034:80
- zjgx: 20033:80

### 3.2 Bridge服务端口（内部通信）
- naoer-bridge: HTTP:22333
- chifeng-bridge: HTTP:22334
- jtgf-bridge: HTTP:22222
- zjgx-bridge: HTTP:22222

### 3.3 配置文件同步
各项目的web配置文件中JAVA_HOSTS必须与bridge端口一致：
```php
// naoer/web/env/naoer.php
define("JAVA_HOSTS", "icbc-naoer-bridge:22333");

// chifeng/web/env/cf.php  
define("JAVA_HOSTS", "icbc-chifeng-bridge:22334");

// jtgf/web/env/jtgf.php
define("JAVA_HOSTS", "icbc-jtgf-bridge:22222");

// zjgx/web/env/zjgx.php
define("JAVA_HOSTS", "icbc-zjgx-bridge:22222");
```

## 4. 测试规范

### 4.1 域名访问限制
**重要说明**: 所有ICBC项目只允许通过配置的域名访问，不允许通过IP或其他域名访问。直接使用IP访问会返回500错误。

### 4.2 标准测试方法

#### 4.2.1 基础连通性测试
```bash
# 测试主页访问
curl -I http://naoer.icbc.51zsqc.com/
curl -I http://cf.icbc.51zsqc.com/
curl -I http://jtgf.icbc.51zsqc.com/
curl -I http://zjgx.icbc.51zsqc.com/

# 期望结果: HTTP/1.1 200 OK
```

#### 4.2.2 关键接口测试

**1. createTask接口测试**
```bash
# 创建同步任务
curl -I http://naoer.icbc.51zsqc.com/index.php/icbc/crontab/createTask
curl -I http://cf.icbc.51zsqc.com/index.php/icbc/crontab/createTask
curl -I http://jtgf.icbc.51zsqc.com/index.php/icbc/crontab/createTask
curl -I http://zjgx.icbc.51zsqc.com/index.php/icbc/crontab/createTask

# 期望结果: HTTP/1.1 200 OK
```

**2. syncAmount接口测试（可能耗时较长）**
```bash
# 同步用户余额 - 注意：此接口可能运行时间较长
timeout 30s curl http://naoer.icbc.51zsqc.com/index.php/icbc/crontab/syncAmount
timeout 30s curl http://cf.icbc.51zsqc.com/index.php/icbc/crontab/syncAmount
timeout 30s curl http://jtgf.icbc.51zsqc.com/index.php/icbc/crontab/syncAmount
timeout 30s curl http://zjgx.icbc.51zsqc.com/index.php/icbc/crontab/syncAmount

# 期望结果: 接口开始执行（可能超时，但这是正常的）
```

#### 4.2.3 项目健康状态判断
**判断标准**:
1. createTask接口返回200 OK
2. syncAmount接口能够响应（即使超时）
3. 如果两个接口都无响应，则认为项目有问题

## 5. 定时任务配置

### 5.1 crontab任务
各项目都配置了相应的定时任务：

**naoer项目**:
```bash
# SFTP检测
*/10 * * * * curl http://naoer.icbc.51zsqc.com/index.php/icbc/crontab/checkSftp
# 创建同步任务
00 4 * * * curl http://naoer.icbc.51zsqc.com/index.php/icbc/crontab/createTask
# 同步用户余额
*/1 * * * * curl http://naoer.icbc.51zsqc.com/index.php/icbc/crontab/syncAmount
# 创建代扣任务
00 9 * * * curl http://naoer.icbc.51zsqc.com/index.php/icbc/crontab/createDaiKou
# 导出用户文件
* * * * * curl http://naoer.icbc.51zsqc.com/index.php/icbc/crontab/exportUser
# 删除导出文件
* * * * * curl http://naoer.icbc.51zsqc.com/index.php/icbc/crontab/deleteExportFile
```

**chifeng项目**:
```bash
# SFTP检测
*/10 * * * * curl http://cf.icbc.51zsqc.com/index.php/icbc/crontab/checkSftp
# 创建同步任务
00 4 * * * curl http://cf.icbc.51zsqc.com/index.php/icbc/crontab/createTask
# 同步用户余额
*/1 * * * * curl http://cf.icbc.51zsqc.com/index.php/icbc/crontab/syncAmount
# 创建代扣任务（每3天）
00 9 */3 * * curl http://cf.icbc.51zsqc.com/index.php/icbc/crontab/createDaiKou
```

**zjgx项目**:
```bash
# SFTP检测
*/10 * * * * curl http://zjgx.icbc.51zsqc.com/index.php/icbc/crontab/checkSftp
# 创建同步任务
00 4 * * * curl http://zjgx.icbc.51zsqc.com/index.php/icbc/crontab/createTask
# 同步用户余额
*/1 * * * * curl http://zjgx.icbc.51zsqc.com/index.php/icbc/crontab/syncAmount
```

## 6. 容器管理

### 6.1 管理脚本使用
每个项目都提供了standardized管理脚本：

```bash
cd /mnt/datadisk0/apps/icbc-{项目名}

# 查看状态
./manage.sh status

# 启动服务
./manage.sh start

# 停止服务
./manage.sh stop

# 重启服务
./manage.sh restart

# 查看日志
./manage.sh logs

# 进入容器
./manage.sh shell

# 删除容器
./manage.sh remove
```

### 6.2 容器依赖关系
- web服务依赖bridge服务
- bridge服务必须先启动成功，web服务才能正常工作

## 7. 数据库和外部依赖

### 7.1 数据库配置
所有项目共享相同的数据库服务器：
- **主机**: mysql-prod
- **端口**: 3306
- 各项目使用独立的数据库和用户

### 7.2 Redis配置
共享Redis服务器：
- **主机**: redis-prod
- **端口**: 6379

### 7.3 第三方服务
- **百度SMS**: 短信发送服务
- **百度OCR**: 身份证识别服务
- **微信API**: 微信小程序接口
- **ICBC SFTP**: 工商银行文件传输服务

## 8. 监控和维护

### 8.1 日常检查项目
1. **容器状态检查**
   ```bash
   docker ps --filter name=icbc
   ```

2. **服务可用性检查**
   - 使用本文档第4节的测试方法

3. **日志监控**
   ```bash
   # 检查应用日志
   ./manage.sh logs
   
   # 检查Apache错误日志
   tail -f /mnt/datadisk0/apps/icbc-{项目名}/logs/error.log
   ```

### 8.2 故障排查流程
1. 检查容器是否运行
2. 检查bridge和web服务的端口配置是否匹配
3. 测试域名访问和关键接口
4. 检查数据库和Redis连接
5. 查看详细日志定位问题

## 9. 部署和扩展

### 9.1 新项目部署流程
1. 创建标准化目录结构
2. 配置docker-compose.yml
3. 设置环境变量和配置文件
4. 启动容器服务
5. 配置域名解析
6. 设置crontab定时任务
7. 执行测试验证

### 9.2 配置模板
参考现有的naoer和chifeng项目配置作为模板。

## 10. 安全注意事项

1. **域名限制**: 所有应用仅允许通过指定域名访问
2. **网络隔离**: 使用独立Docker网络隔离不同项目
3. **敏感信息**: 数据库密码、API密钥等敏感信息存储在env配置文件中
4. **文件权限**: 确保配置文件和数据目录权限设置正确

## 11. 版本信息

- **文档版本**: 1.0
- **创建日期**: 2025-07-25
- **更新日期**: 2025-07-25
- **适用范围**: ICBC光伏项目全系列

## 12. 联系信息

- **维护团队**: 技术运维团队
- **紧急联系**: 7x24小时技术支持
- **文档维护**: 随系统更新同步维护