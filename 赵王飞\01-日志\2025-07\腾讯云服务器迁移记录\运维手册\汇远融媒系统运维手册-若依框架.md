# 汇远融媒系统运维手册 - 若依框架

## 项目概览

**系统名称**: 汇远融媒体内容管理系统  
**技术框架**: 若依框架 (RuoYi)  
**访问域名**: hyrm.nmzyb.cn  
**技术架构**: Spring Boot + MySQL + Redis + RabbitMQ  
**服务器环境**: Ubuntu 24.04 + Docker容器化部署  
**数据库**: ruoyi_hyrm (~3.2GB)

## 服务架构

```
外部访问 → 雷池WAF → 1Panel OpenResty → ruoyi-hyrm容器
                                      ↓
                    MySQL + Redis + RabbitMQ 容器
```

### 核心容器服务

| 容器名称 | 镜像 | 端口映射 | 用途 |
|---------|------|----------|------|
| ruoyi-hyrm | 4525e4716f8b | 8082:80 | 汇远融媒主应用 |
| mysql-prod | mysql:5.7.44 | 3306 | 数据库服务 |
| redis-prod | redis:8.0.3 | 6379 | 缓存服务 |
| rabbitmq-prod | rabbitmq:management | 5672:5672, 15672:15672 | 消息队列服务 |

## 目录结构

```
/mnt/datadisk0/
├── apps/java-web/ruoyi-hyrm/
│   ├── docker-compose.yml               # 融媒主应用容器配置
│   └── conf/application-prod.yml        # 生产环境配置
├── volumns/java-web/ruoyi-hyrm/         # 融媒应用数据目录
│   ├── workdir/                         # 应用工作目录
│   ├── ui/                              # 前端静态文件(ruoyi-ui)
│   └── logs/                            # 应用日志(/home/<USER>/logs)
└── backups/hyrm/                        # 备份文件
```

## 数据库信息

### 主要数据库
- **数据库名**: 使用若依默认数据库配置
- **业务**: 汇远融媒体内容管理
- **配置**: Spring profiles active=prod
- **用途**: 融媒体内容管理、文章发布、媒体文件管理

### 数据库连接信息
- **主机**: mysql-prod (容器内网络)
- **用户**: pearproject
- **密码**: kG7#tPq9@zR2$vX1
- **端口**: 3306

### 核心数据表
```sql
-- 内容管理相关表
cms_article          -- 文章内容表
cms_category         -- 内容分类表
cms_media_file       -- 媒体文件表
cms_publish_log      -- 发布日志表

-- 系统管理相关表  
sys_user             -- 用户表
sys_role             -- 角色表
sys_menu             -- 菜单权限表
sys_logininfor       -- 登录日志表
sys_file_info        -- 文件信息表
```

## 日常维护操作

### 1. 服务状态检查

```bash
# 检查融媒应用容器状态
docker ps | grep "ruoyi-hyrm"

# 检查容器资源使用情况
docker stats --no-stream ruoyi-hyrm

# 检查应用端口
netstat -tlnp | grep 8082

# 检查JVM内存使用
docker exec ruoyi-hyrm jstat -gc $(docker exec ruoyi-hyrm jps | grep -v Jps | awk '{print $1}')
```

### 2. 应用日志查看

#### Spring Boot应用日志
```bash
# 融媒应用容器日志
docker logs ruoyi-hyrm --tail 100 -f

# Spring Boot内部日志文件
docker exec ruoyi-hyrm tail -f /home/<USER>/logs/spring.log

# 错误日志
docker exec ruoyi-hyrm tail -f /home/<USER>/logs/error.log

# 业务日志
docker exec ruoyi-hyrm tail -f /home/<USER>/logs/sys-info.log
```

#### 1Panel网站访问日志
```bash
# 融媒访问日志
tail -f /opt/1panel/www/sites/hyrm.nmzyb.cn/log/access.log

# 错误日志
tail -f /opt/1panel/www/sites/hyrm.nmzyb.cn/log/error.log

# 统计今日访问量
grep "$(date '+%d/%b/%Y')" /opt/1panel/www/sites/hyrm.nmzyb.cn/log/access.log | wc -l

# 分析访问IP统计
awk '{print $1}' /opt/1panel/www/sites/hyrm.nmzyb.cn/log/access.log | sort | uniq -c | sort -nr | head -10
```

### 3. 数据库维护

```bash
# 连接融媒数据库
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm

# 查看数据库大小
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' -e "
SELECT 
    table_schema as 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'ruoyi_hyrm';"

# 查看表占用空间排序
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as 'Size (MB)'
FROM information_schema.TABLES 
WHERE table_schema = 'ruoyi_hyrm'
ORDER BY (data_length + index_length) DESC LIMIT 10;"

# 备份融媒数据库
docker exec mysql-prod mysqldump -u root -p'56d9DavJ*zwrwj9rmA' --single-transaction --routines --triggers ruoyi_hyrm > /tmp/ruoyi_hyrm_$(date +%Y%m%d).sql
```

### 4. 缓存管理

```bash
# Redis缓存状态
docker exec redis-prod redis-cli INFO memory

# 查看融媒相关缓存键
docker exec redis-prod redis-cli KEYS "*hyrm*"
docker exec redis-prod redis-cli KEYS "*ruoyi*"

# 查看用户会话缓存
docker exec redis-prod redis-cli KEYS "*login_tokens*"
docker exec redis-prod redis-cli KEYS "*captcha*"

# 查看菜单缓存
docker exec redis-prod redis-cli KEYS "*sys_menu*"
docker exec redis-prod redis-cli KEYS "*sys_user*"

# 清理过期缓存
docker exec redis-prod redis-cli KEYS "*expired*" | xargs docker exec redis-prod redis-cli DEL

# 清理融媒特定缓存
docker exec redis-prod redis-cli DEL $(docker exec redis-prod redis-cli KEYS "*hyrm*")
```

### 5. 消息队列管理

```bash
# 检查RabbitMQ状态
docker exec rabbitmq-prod rabbitmqctl status

# 查看融媒相关队列
docker exec rabbitmq-prod rabbitmqctl list_queues | grep -i hyrm

# 查看消息积压情况
docker exec rabbitmq-prod rabbitmqctl list_queues name messages | grep -v -E "^0$"

# 访问RabbitMQ管理界面
echo "RabbitMQ管理界面: http://服务器IP:15672"
echo "默认用户名/密码: guest/guest"
```

## 业务特定操作

### 融媒体内容管理

```bash
# 检查内容发布状态
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT status, COUNT(*) as count 
FROM cms_article 
WHERE created_time > CURDATE() 
GROUP BY status;"

# 查看最新发布的文章
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT id, title, author, status, created_time 
FROM cms_article 
ORDER BY created_time DESC 
LIMIT 10;"

# 查看文章分类统计
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT c.category_name, COUNT(a.id) as article_count
FROM cms_category c 
LEFT JOIN cms_article a ON c.id = a.category_id 
GROUP BY c.id, c.category_name 
ORDER BY article_count DESC;"

# 检查媒体文件上传情况
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT DATE(upload_time) as date, COUNT(*) as upload_count, 
       SUM(file_size)/1024/1024 as 'Total_MB'
FROM sys_file_info 
WHERE upload_time > DATE_SUB(NOW(), INTERVAL 7 DAY) 
GROUP BY DATE(upload_time) 
ORDER BY date DESC;"
```

### 用户管理监控

```bash
# 查看系统用户活跃度
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT DATE(login_date) as date, COUNT(*) as login_count 
FROM sys_logininfor 
WHERE login_date > DATE_SUB(NOW(), INTERVAL 7 DAY) 
GROUP BY DATE(login_date) 
ORDER BY date DESC;"

# 查看在线用户数
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT COUNT(DISTINCT user_name) as online_users 
FROM sys_user_online 
WHERE expire_time > NOW();"

# 检查用户权限分布
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT r.role_name, COUNT(ur.user_id) as user_count
FROM sys_role r 
LEFT JOIN sys_user_role ur ON r.role_id = ur.role_id 
GROUP BY r.role_id, r.role_name 
ORDER BY user_count DESC;"
```

### 系统操作日志

```bash
# 查看系统操作日志
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT oper_name, business_type, method, oper_time 
FROM sys_oper_log 
WHERE oper_time > DATE_SUB(NOW(), INTERVAL 1 DAY) 
ORDER BY oper_time DESC 
LIMIT 20;"

# 查看登录失败记录
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT user_name, ipaddr, login_location, msg, login_time 
FROM sys_logininfor 
WHERE status = '1' AND login_time > DATE_SUB(NOW(), INTERVAL 1 DAY) 
ORDER BY login_time DESC;"
```

## 代码更新部署

```bash
# 进入融媒应用目录
cd /mnt/datadisk0/volumns/java-web/ruoyi-hyrm

# 更新代码
git pull origin master

# 重新构建JAR包（如果需要）
mvn clean package -DskipTests

# 备份当前版本
cp target/ruoyi-admin.jar target/ruoyi-admin.jar.backup.$(date +%Y%m%d)

# 重启融媒应用容器
docker restart ruoyi-hyrm

# 检查启动状态
docker logs ruoyi-hyrm --tail 50

# 验证服务可用性
curl -I http://hyrm.nmzyb.cn/
```

## 故障排查

### 常见问题

1. **Spring Boot应用启动失败**
   ```bash
   # 检查JVM内存设置
   docker exec ruoyi-hyrm java -XX:+PrintFlagsFinal -version | grep HeapSize
   
   # 查看详细启动日志
   docker logs ruoyi-hyrm --tail 200
   
   # 检查配置文件
   docker exec ruoyi-hyrm cat /workdir/application.yml
   docker exec ruoyi-hyrm cat /workdir/application-prod.yml
   ```

2. **数据库连接超时**
   ```bash
   # 检查数据库连接池状态
   docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' -e "SHOW PROCESSLIST;"
   
   # 检查融媒应用的数据库连接
   docker exec ruoyi-hyrm netstat -an | grep 3306
   
   # 重启应用容器
   docker restart ruoyi-hyrm
   ```

3. **文件上传失败**
   ```bash
   # 检查上传目录权限
   docker exec ruoyi-hyrm ls -la /workdir/upload/
   
   # 检查磁盘空间
   df -h /mnt/datadisk0
   
   # 清理临时文件
   docker exec ruoyi-hyrm find /workdir/temp -mtime +1 -delete
   
   # 检查Nginx上传限制
   docker exec 1Panel-openresty-YFc6 cat /etc/nginx/nginx.conf | grep client_max_body_size
   ```

4. **若依框架特定问题**
   ```bash
   # 检查代码生成器状态
   docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
   SELECT table_name, table_comment, create_time 
   FROM gen_table 
   ORDER BY create_time DESC LIMIT 5;"
   
   # 检查定时任务状态
   docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
   SELECT job_name, job_group, cron_expression, status 
   FROM sys_job;"
   
   # 检查字典数据
   docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
   SELECT dict_type, dict_label, dict_value, status 
   FROM sys_dict_data 
   WHERE status = '0' 
   ORDER BY dict_sort;"
   ```

## 性能监控

### JVM监控

```bash
# 查看JVM内存使用
docker exec ruoyi-hyrm jstat -gc $(docker exec ruoyi-hyrm jps | grep -v Jps | awk '{print $1}') 5s

# 查看线程数
docker exec ruoyi-hyrm jstack $(docker exec ruoyi-hyrm jps | grep -v Jps | awk '{print $1}') | grep "java.lang.Thread.State" | wc -l

# CPU使用监控
docker exec ruoyi-hyrm top -p $(docker exec ruoyi-hyrm jps | grep -v Jps | awk '{print $1}')

# 内存dump分析（出现OOM时）
docker exec ruoyi-hyrm jmap -dump:format=b,file=/workdir/heapdump.hprof $(docker exec ruoyi-hyrm jps | grep -v Jps | awk '{print $1}')
```

### 应用性能监控

```bash
# 接口响应时间监控
curl -w "@curl-format.txt" -o /dev/null -s "http://hyrm.nmzyb.cn/health"

# 数据库慢查询监控
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' -e "
SELECT query_time, lock_time, rows_sent, rows_examined, 
       LEFT(sql_text, 100) as sql_preview
FROM mysql.slow_log 
WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 HOUR) 
    AND db = 'ruoyi_hyrm'
ORDER BY query_time DESC LIMIT 5;"

# Druid连接池监控
curl -s "http://hyrm.nmzyb.cn/druid/index.html"
```

## 备份策略

### 数据备份
- **数据库**: 每日凌晨4点自动备份
- **上传文件**: 每日增量备份  
- **代码**: Git仓库备份
- **配置文件**: 每日备份

### 备份脚本
```bash
#!/bin/bash
# 汇远融媒备份脚本
DATE=$(date +%Y%m%d)
BACKUP_DIR="/mnt/datadisk0/backups/hyrm/$DATE"
mkdir -p $BACKUP_DIR

# 数据库备份
docker exec mysql-prod mysqldump -u root -p'56d9DavJ*zwrwj9rmA' \
    --single-transaction --routines --triggers ruoyi_hyrm > $BACKUP_DIR/ruoyi_hyrm.sql

# 配置备份
cp -r /mnt/datadisk0/apps/java-web/ruoyi-hyrm $BACKUP_DIR/

# 上传文件备份
rsync -av /mnt/datadisk0/volumns/java-web/ruoyi-hyrm/workdir/upload/ $BACKUP_DIR/uploads/

# 日志备份（保留最近7天）
find /mnt/datadisk0/volumns/java-web/ruoyi-hyrm/logs -name "*.log" -mtime -7 -exec cp {} $BACKUP_DIR/logs/ \;

echo "汇远融媒备份完成: $BACKUP_DIR"
```

## 安全配置

### 访问控制
```bash
# 检查防火墙规则
ufw status

# 检查登录失败记录（防暴力破解）
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT ipaddr, COUNT(*) as failed_attempts, MAX(login_time) as last_attempt
FROM sys_logininfor 
WHERE status = '1' AND login_time > DATE_SUB(NOW(), INTERVAL 1 DAY) 
GROUP BY ipaddr 
HAVING failed_attempts > 5
ORDER BY failed_attempts DESC;"

# 检查异常操作记录
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT oper_name, business_type, method, oper_ip, oper_time
FROM sys_oper_log 
WHERE status = 1 AND oper_time > DATE_SUB(NOW(), INTERVAL 1 DAY)
ORDER BY oper_time DESC LIMIT 10;"
```

### 数据安全
```bash
# 检查敏感数据加密状态
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT COUNT(*) as encrypted_users 
FROM sys_user 
WHERE password LIKE '\$2a\$%';"

# 检查权限配置
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT u.user_name, r.role_name, r.role_key, u.status
FROM sys_user u 
JOIN sys_user_role ur ON u.user_id = ur.user_id 
JOIN sys_role r ON ur.role_id = r.role_id 
WHERE u.status = '0' AND r.status = '0'
ORDER BY u.user_name;"
```

## 若依框架特色功能维护

### 代码生成器
```bash
# 查看生成的表配置
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT table_name, table_comment, business_name, class_name, create_time
FROM gen_table 
ORDER BY create_time DESC;"

# 清理生成的临时文件
docker exec ruoyi-hyrm find /workdir/gen -name "*.zip" -mtime +7 -delete
```

### 定时任务管理
```bash
# 查看定时任务执行状态
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT job_name, job_group, cron_expression, status, 
       next_valid_time, create_time
FROM sys_job 
ORDER BY create_time DESC;"

# 查看定时任务执行日志
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT job_name, job_group, status, job_message, create_time
FROM sys_job_log 
WHERE create_time > DATE_SUB(NOW(), INTERVAL 1 DAY)
ORDER BY create_time DESC LIMIT 10;"
```

## 更新记录

| 日期 | 版本 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 2025-07-30 | v1.0 | 基于若依框架的融媒系统独立运维手册 | Claude |

---
**维护负责人**: 运维团队  
**紧急联系**: 技术支持部门  
**业务联系人**: 汇远融媒产品团队