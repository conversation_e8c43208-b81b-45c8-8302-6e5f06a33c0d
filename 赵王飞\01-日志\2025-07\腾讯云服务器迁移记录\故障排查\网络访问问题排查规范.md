# 网络访问问题排查规范

**创建时间**: 2025-07-29  
**适用场景**: 雷池WAF + 1Panel OpenResty + 容器化架构  
**架构概述**: 外部请求 → 雷池WAF → 1Panel OpenResty → 容器应用

## 📋 排查原则

### 核心理念
- **分层诊断**: 按请求链路逐层排查，确定故障发生的具体环节
- **日志优先**: 通过日志分析问题，避免盲目猜测
- **最小变更**: 优先使用查看命令，避免修改配置增加复杂度

### 请求链路
```
客户端 → DNS解析 → 雷池WAF → 1Panel OpenResty → 容器应用 → 数据库/缓存
```

## 🔍 标准排查流程

### 第一阶段：基础网络验证

#### 1.1 DNS解析检查
```bash
# 验证域名解析
nslookup [域名]
dig [域名]

# 确认解析结果是否指向正确的服务器IP
# 预期结果：域名应解析到服务器公网IP
```

#### 1.2 基础连通性测试
```bash
# 简单HTTP请求测试
curl -I http://[域名]/

# 分析返回的状态码和Server头信息
# 403 Forbidden: 通常是配置问题，继续后续排查
# 502 Bad Gateway: 后端服务问题
# 504 Gateway Timeout: 后端服务响应超时
# Connection refused: 端口未开放或服务未启动
```

### 第二阶段：雷池WAF层面排查

#### 2.1 WAF站点配置检查
```bash
# 查看雷池WAF中配置的所有站点
docker exec safeline-tengine ls /etc/nginx/sites-enabled/

# 搜索目标域名是否在配置中
docker exec safeline-tengine grep -r "[域名]" /etc/nginx/sites-enabled/

# 检查具体站点配置
docker exec safeline-tengine cat /etc/nginx/sites-enabled/IF_backend_[ID]
```

#### 2.2 WAF日志分析
```bash
# 查看雷池访问日志
docker exec safeline-tengine tail -100 /var/log/nginx/safeline/accesslog_[ID]

# 查看雷池错误日志  
docker exec safeline-tengine tail -100 /var/log/nginx/safeline/errorlog_[ID]

# 实时监控访问日志
docker exec safeline-tengine tail -f /var/log/nginx/safeline/accesslog_[ID]
```

#### 2.3 WAF规则验证
```bash
# 检查站点的server_name配置
docker exec safeline-tengine grep "server_name" /etc/nginx/sites-enabled/IF_backend_[ID]

# 检查upstream后端配置
docker exec safeline-tengine grep -A 5 "upstream backend_[ID]" /etc/nginx/sites-enabled/IF_backend_[ID]

# 验证后端服务器IP和端口
# 通常应该是1Panel OpenResty的内网IP:180
```

### 第三阶段：1Panel OpenResty层面排查

#### 3.1 1Panel站点配置检查
```bash
# 检查1Panel中是否有对应的站点配置
ls -la /opt/1panel/www/conf.d/ | grep [域名]

# 查看站点nginx配置
cat /opt/1panel/www/conf.d/[域名].conf

# 检查代理配置
cat /opt/1panel/www/sites/[域名]/proxy/root.conf
```

#### 3.2 1Panel访问日志分析
```bash
# 查看1Panel访问日志
tail -100 /opt/1panel/www/sites/[域名]/log/access.log

# 查看1Panel错误日志
tail -100 /opt/1panel/www/sites/[域名]/log/error.log

# 实时监控日志
tail -f /opt/1panel/www/sites/[域名]/log/access.log

# 筛选特定时间段的访问记录
grep "$(date '+%d/%b/%Y:%H')" /opt/1panel/www/sites/[域名]/log/access.log
```

#### 3.3 1Panel服务状态检查
```bash
# 检查1Panel OpenResty容器状态
docker ps | grep openresty

# 检查1Panel容器日志
docker logs 1Panel-openresty-[ID] --tail 100

# 验证1Panel内部端口监听
docker exec 1Panel-openresty-[ID] netstat -tlnp | grep 180
```

### 第四阶段：容器应用层面排查

#### 4.1 目标容器状态检查
```bash
# 检查目标应用容器状态
docker ps | grep [应用容器名]

# 查看容器启动日志
docker logs [应用容器名] --tail 100

# 检查容器内端口监听
docker exec [应用容器名] netstat -tlnp
```

#### 4.2 应用内部验证
```bash
# 容器内部测试应用响应
docker exec [应用容器名] curl -I http://localhost:[端口]/

# 检查应用配置文件
docker exec [应用容器名] ls -la /var/www/html/
docker exec [应用容器名] cat [配置文件路径]
```

#### 4.3 网络连通性测试
```bash
# 从1Panel OpenResty容器测试应用容器连通性
docker exec 1Panel-openresty-[ID] curl -I http://[应用容器名]:[端口]/

# 验证容器网络配置
docker inspect [应用容器名] | grep -A 10 NetworkSettings
docker network ls
docker network inspect 1panel-network
```

### 第五阶段：数据库/缓存层面排查

#### 5.1 数据库连接测试
```bash
# 测试数据库连接
docker exec [应用容器名] mysql -h mysql-prod -u [用户名] -p[密码] -e "SELECT 1"

# 检查数据库容器状态
docker logs mysql-prod --tail 100
```

#### 5.2 缓存服务测试
```bash
# 测试Redis连接
docker exec [应用容器名] redis-cli -h redis-prod ping

# 测试Memcached连接
docker exec [应用容器名] echo "stats" | nc memcached-prod 11211
```

## 🚨 常见问题诊断

### 403 Forbidden 错误

#### 症状表现
- HTTP状态码：403
- 页面显示：403 Forbidden
- Server头：nginx

#### 排查重点
1. **域名配置不匹配**（最常见）
   ```bash
   # 检查域名是否在雷池WAF中配置
   docker exec safeline-tengine grep -r "[域名]" /etc/nginx/sites-enabled/
   ```

2. **1Panel站点不存在**
   ```bash
   # 检查1Panel是否有对应站点
   ls /opt/1panel/www/conf.d/[域名].conf
   ```

3. **WAF安全规则拦截**
   ```bash
   # 查看WAF拦截日志
   docker exec safeline-tengine grep "403" /var/log/nginx/safeline/accesslog_[ID]
   ```

#### 解决方案
- 在雷池WAF管理界面添加域名到现有站点
- 在1Panel管理界面创建对应的反向代理站点
- 检查并调整WAF安全规则

### 502 Bad Gateway 错误

#### 症状表现
- HTTP状态码：502
- 页面显示：502 Bad Gateway

#### 排查重点
1. **后端容器未启动**
   ```bash
   docker ps | grep [应用容器名]
   ```

2. **代理配置错误**
   ```bash
   cat /opt/1panel/www/sites/[域名]/proxy/root.conf
   ```

3. **网络连通性问题**
   ```bash
   docker exec 1Panel-openresty-[ID] curl http://[容器名]:[端口]/
   ```

### 504 Gateway Timeout 错误

#### 症状表现
- HTTP状态码：504
- 页面显示：504 Gateway Timeout

#### 排查重点
1. **应用响应缓慢**
2. **数据库连接问题**
3. **资源不足**


## ⚠️ 注意事项

### 安全提醒
- 排查过程中避免暴露敏感信息（密码、密钥等）
- 不要在生产环境进行破坏性测试
- 记录所有操作步骤，便于回溯

### 效率建议
- 按照排查流程顺序执行，避免跳跃式排查
- 优先使用日志分析，减少盲目尝试
- 建立问题知识库，积累常见问题解决经验

### 协作规范
- 问题排查过程要及时记录和共享
- 重大问题排查前要备份关键配置
- 解决方案要经过验证后再应用到其他环境

---
**版本**: v1.0  
**维护者**: 运维团队  
**更新日期**: 2025-07-29