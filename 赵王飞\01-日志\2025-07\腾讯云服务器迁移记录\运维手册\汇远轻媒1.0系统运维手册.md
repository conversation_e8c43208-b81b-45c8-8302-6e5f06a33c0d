# 汇远轻媒1.0系统运维手册

## 项目概览

**系统名称**: 汇远轻媒1.0系统  
**访问域名**: hyqm.nmzyb.cn + qm.nmzyb.cn  
**技术架构**: Spring Boot + MySQL + Redis  
**服务器环境**: Ubuntu 24.04 + Docker容器化部署  
**数据库**: hyqm (~1.5GB) - 轻量级媒体管理

## 服务架构

```
外部访问 → 雷池WAF → 1Panel OpenResty → hyqm容器
                                      ↓
                    MySQL + Redis 容器
```

### 核心容器服务

| 容器名称 | 镜像 | 端口映射 | 用途 |
|---------|------|----------|------|
| hyqm | zhao0829wang/java-nginx:1.0 | 8086:80, 8087:8087 | 汇远轻媒1.0(双域名) |
| mysql-prod | mysql:5.7.44 | 3306 | 数据库服务 |
| redis-prod | redis:8.0.3 | 6379 | 缓存服务 |

## 目录结构

```
/mnt/datadisk0/
├── apps/java-web/hyqm/
│   ├── docker-compose-hyqm.yml         # 轻媒1.0容器配置
│   └── .env                             # 环境变量
├── volumns/java-web/hyqm/               # 轻媒1.0应用数据目录
│   ├── workdir/                         # 应用工作目录
│   ├── ui/                              # 前端静态文件
│   └── logs/                            # 应用日志
└── backups/hyqm/                        # 备份文件
```

## 数据库信息

### 主要数据库
- **数据库名**: hyqm
- **业务**: 汇远轻媒1.0
- **配置**: Spring profiles active=prod, server.port=48080
- **用途**: 轻量级媒体管理

### 数据库连接信息
- **主机**: mysql-prod (容器内网络)
- **用户**: pearproject
- **密码**: kG7#tPq9@zR2$vX1
- **端口**: 3306

## 服务状态检查

```bash
# 检查轻媒1.0容器状态
docker ps | grep "hyqm"

# 检查容器资源使用情况
docker stats --no-stream hyqm

# 检查应用端口
netstat -tlnp | grep -E "(8086|8087)"

# 检查JVM内存使用(注:此镜像可能不包含jstat命令)
# docker exec hyqm jstat -gc $(docker exec hyqm jps | grep -v Jps | awk '{print $1}')

# 检查容器健康状态
docker inspect hyqm | grep -A 5 "Health"

# 检查进程状态
docker exec hyqm ps aux | grep java
```

## 日志查看

### Spring Boot应用日志
```bash
# 轻媒1.0应用容器日志
docker logs hyqm --tail 100 -f

# 实时查看最新日志
docker logs hyqm --tail 50 --follow

# 查看特定时间段日志
docker logs hyqm --since="2h" --until="1h"

# Spring Boot内部日志文件
docker exec hyqm tail -f /home/<USER>/logs/spring.log

# 业务日志
docker exec hyqm tail -f /home/<USER>/logs/biz.log

# 错误日志
docker exec hyqm tail -f /home/<USER>/logs/error.log

# 查看日志文件大小
docker exec hyqm du -sh /home/<USER>/logs/*

# 搜索特定关键词日志
docker exec hyqm grep -n "ERROR" /home/<USER>/logs/spring.log | tail -20
docker exec hyqm grep -n "Exception" /home/<USER>/logs/error.log | tail -10
```

### 1Panel网站访问日志
```bash
# 轻媒访问日志
tail -f /opt/1panel/www/sites/hyqm.nmzyb.cn/log/access.log

# 错误日志
tail -f /opt/1panel/www/sites/hyqm.nmzyb.cn/log/error.log

# 统计今日访问量
grep "$(date '+%d/%b/%Y')" /opt/1panel/www/sites/hyqm.nmzyb.cn/log/access.log | wc -l

# 查看最近访问记录
tail -20 /opt/1panel/www/sites/hyqm.nmzyb.cn/log/access.log

# 分析访问IP统计
awk '{print $1}' /opt/1panel/www/sites/hyqm.nmzyb.cn/log/access.log | sort | uniq -c | sort -nr | head -10

# 查看状态码分布
awk '{print $9}' /opt/1panel/www/sites/hyqm.nmzyb.cn/log/access.log | sort | uniq -c | sort -nr

# 查看访问频率最高的页面
awk '{print $7}' /opt/1panel/www/sites/hyqm.nmzyb.cn/log/access.log | sort | uniq -c | sort -nr | head -10

# 实时监控访问日志（彩色显示）
tail -f /opt/1panel/www/sites/hyqm.nmzyb.cn/log/access.log | grep --color=always -E "(GET|POST|PUT|DELETE)"

# 查看特定时间段的访问日志
sed -n '/30\/Jul\/2025:08:00:00/,/30\/Jul\/2025:09:00:00/p' /opt/1panel/www/sites/hyqm.nmzyb.cn/log/access.log
```

### 系统日志监控
```bash
# 查看系统级容器日志
journalctl -u docker -f | grep hyqm

# 监控容器资源使用实时变化
watch -n 2 'docker stats --no-stream hyqm'

# 查看磁盘I/O情况(注:此镜像可能不包含iostat命令)
# docker exec hyqm iostat -x 1 3

# 查看网络连接状态
docker exec hyqm netstat -tuln | grep :48080

# 监控JVM GC日志(在容器内的实际路径可能不同)
# docker exec hyqm tail -f /home/<USER>/logs/gc.log
```

## 业务特定日志

### 轻媒内容管理日志
```bash
# 查看内容操作日志
docker exec hyqm grep -n "content" /home/<USER>/logs/biz.log | tail -20

# 查看用户操作日志
docker exec hyqm grep -n "user_operation" /home/<USER>/logs/biz.log | tail -20

# 查看文件上传日志
docker exec hyqm grep -n "upload" /home/<USER>/logs/biz.log | tail -20

# 查看登录相关日志
docker exec hyqm grep -n "login" /home/<USER>/logs/spring.log | tail -20
```

### 应用启动和运行状态日志
```bash
# 查看应用启动日志
docker logs hyqm | grep -A 10 -B 5 "Started Application"

# 查看Spring Boot健康检查日志
docker exec hyqm curl -s http://localhost:48080/actuator/health | jq .

# 查看应用环境信息
docker exec hyqm curl -s http://localhost:48080/actuator/env | jq .

# 监控应用指标
docker exec hyqm curl -s http://localhost:48080/actuator/metrics | jq .
```

## 日志轮转和清理

```bash
# 查看日志文件大小统计
docker exec hyqm find /home/<USER>/logs -name "*.log" -exec ls -lh {} \; | sort -k5 -hr

# 压缩旧日志文件
docker exec hyqm find /home/<USER>/logs -name "*.log.*" -mtime +7 -exec gzip {} \;

# 清理过旧的压缩日志
docker exec hyqm find /home/<USER>/logs -name "*.gz" -mtime +30 -delete

# 检查日志目录使用情况
docker exec hyqm du -sh /home/<USER>/logs
```

## 更新记录

| 日期 | 版本 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 2025-07-30 | v1.0 | 汇远轻媒1.0系统精简运维手册 | Claude |

---
**维护负责人**: 运维团队  
**紧急联系**: 技术支持部门  
**业务联系人**: 汇远轻媒产品团队