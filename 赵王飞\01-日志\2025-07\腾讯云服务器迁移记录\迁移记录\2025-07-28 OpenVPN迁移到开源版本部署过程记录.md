---
date_created: 2025-07-28 14:15:00
date_modified: 2025-07-28 14:15:00  
author: 赵王飞
---

# OpenVPN Access Server迁移到开源OpenVPN计划

## 迁移背景

由于当前OpenVPN Access Server存在并发连接数限制（最大2个连接），导致多用户同时访问时出现"错误凭据"问题。当前OpenVPN运行在Docker容器`1Panel-openvpn-sNji`中，连接到`1panel-network`，使用172.27.x.x网段分配VPN客户端IP。为解决连接数限制问题，计划迁移到开源OpenVPN版本。

## 当前环境概况

### 现有架构
- **容器**: `1Panel-openvpn-sNji` (openvpn/openvpn-as:latest)
- **网络**: 1panel-network (**********/16)，容器IP **********
- **VPN网段**: ************/24 - ************/24 (16个子网)
- **端口映射**: 1194/udp, 943/tcp, 7443/tcp
- **数据目录**: `/opt/1panel/apps/openvpn/openvpn/data`

## 迁移目标

- **主要目标**：消除并发连接数限制，支持更多用户同时使用
- **安全目标**：保持现有安全级别，确保数据传输安全
- **用户体验**：最小化用户端配置变更，保持服务连续性
- **成本优化**：免费开源方案，无许可证费用

## 迁移方案对比

### 当前环境（OpenVPN Access Server）
- **优势**：图形化管理界面、自动证书管理、商业支持
- **劣势**：并发连接数限制、许可证费用、功能限制

### 目标环境（开源OpenVPN）
- **优势**：无连接数限制、免费使用、完全可控、更多配置选项
- **劣势**：需要手动管理、命令行配置、社区支持

## 迁移策略选择

### 方案A：原地容器替换 (推荐)
- 使用相同的端口和网络配置
- 最小化网络架构变更
- 保持1Panel管理集成

### 方案B：新建容器并行运行
- 临时双容器运行，逐步切换
- 风险最低，但资源占用更多

### 方案C：宿主机直接安装
- 脱离容器化架构
- 管理复杂度增加

**选择方案A**：原地容器替换，保持现有网络架构

## 详细执行计划

### 阶段一：容器镜像准备

#### 1.1 备份现有配置
```bash
# 备份整个应用目录
cp -r /opt/1panel/apps/openvpn /opt/1panel/apps/openvpn-backup-$(date +%Y%m%d)

# 备份容器数据
docker exec 1Panel-openvpn-sNji tar czf /openvpn/backup-$(date +%Y%m%d).tar.gz /usr/local/openvpn_as/etc/
```

#### 1.2 选择成熟的开源OpenVPN镜像
经过调研，发现有成熟可信的社区镜像可以直接使用：

**推荐镜像**: `kylemanna/openvpn`
- 超过1000万下载量的成熟镜像
- 内置EasyRSA PKI证书管理
- 活跃维护，2025年仍有更新
- 无连接数限制

**镜像特点**：
- 基于Alpine Linux，体积小
- 自动化证书管理
- 支持Docker Compose
- 与1panel-network兼容

#### 1.3 导出现有用户配置
```bash
# 从Access Server导出用户信息
docker exec 1Panel-openvpn-sNji /usr/local/openvpn_as/scripts/sacli --user list
```

### 阶段二：开源OpenVPN容器配置

#### 2.1 修改Docker Compose配置使用kylemanna/openvpn
```yaml
networks:
    1panel-network:
        external: true
services:
    openvpn:
        cap_add:
            - NET_ADMIN
        container_name: ${CONTAINER_NAME}
        image: kylemanna/openvpn:latest
        labels:
            createdBy: Apps
        networks:
            - 1panel-network
        ports:
            - ${HOST_IP}:${PANEL_APP_PORT_UDP}:1194/udp
        restart: always
        volumes:
            - ./data:/etc/openvpn
        command: ovpn_run
```

#### 2.2 初始化OpenVPN配置
使用kylemanna/openvpn的标准初始化流程：
```bash
# 1. 初始化配置（保持现有VPN网段）
docker run -v /opt/1panel/apps/openvpn/openvpn/data:/etc/openvpn --rm kylemanna/openvpn ovpn_genconfig -u udp://$(curl -s ifconfig.me):1194 -s ************/24 -p "route ********** ***********"

# 2. 生成CA证书和服务器证书
docker run -v /opt/1panel/apps/openvpn/openvpn/data:/etc/openvpn --rm -it kylemanna/openvpn ovpn_initpki

# 3. 生成客户端证书
docker run -v /opt/1panel/apps/openvpn/openvpn/data:/etc/openvpn --rm -it kylemanna/openvpn easyrsa build-client-full user1 nopass
```

#### 2.3 客户端配置文件生成
使用kylemanna/openvpn内置工具生成客户端配置：
```bash
# 为用户生成完整的.ovpn配置文件
docker run -v /opt/1panel/apps/openvpn/openvpn/data:/etc/openvpn --rm kylemanna/openvpn ovpn_getclient user1 > user1.ovpn
```

### 阶段三：切换执行

#### 3.1 停止现有容器
```bash
# 停止Access Server容器
docker stop 1Panel-openvpn-sNji
```

#### 3.2 更新配置文件
```bash
# 修改docker-compose.yml使用新镜像
cd /opt/1panel/apps/openvpn/openvpn/
# 备份原配置
cp docker-compose.yml docker-compose.yml.backup
# 使用新的配置替换
```

#### 3.3 启动新容器
```bash
# 使用相同容器名启动开源OpenVPN
docker compose up -d
```

### 阶段四：网络影响评估

#### 4.1 对1Panel容器网络的影响
**无影响**：
- VPN容器仍在1panel-network中
- 容器间通信保持不变
- 端口映射保持相同

#### 4.2 对VPN客户端的影响
**需要更换配置文件**：
- 服务器地址和端口不变
- 需要新的证书文件
- 配置文件格式从.ovpn改为标准OpenVPN格式

#### 4.3 对服务访问的影响
**最小影响**：
- VPN连接的服务器和端口不变
- 通过VPN访问1Panel内部服务的路由保持不变
- 客户端需要重新下载配置文件

### 阶段四：客户端管理

#### 4.1 为现有用户生成客户端证书和配置
```bash
# 为每个用户生成客户端证书（基于Access Server现有用户）
USERS="user1 user2 admin"

for user in $USERS; do
    echo "Creating client certificate for $user..."
    docker run -v /opt/1panel/apps/openvpn/openvpn/data:/etc/openvpn --rm -it kylemanna/openvpn easyrsa build-client-full $user nopass
    
    echo "Generating client config for $user..."
    docker run -v /opt/1panel/apps/openvpn/openvpn/data:/etc/openvpn --rm kylemanna/openvpn ovpn_getclient $user > ${user}.ovpn
done
```

#### 4.2 验证证书生成
```bash
# 检查生成的配置文件
ls -la *.ovpn

# 验证配置文件内容（查看是否包含完整的证书）
head -20 user1.ovpn
```

### 阶段五：验证与完成

#### 5.1 容器运行状态检查
```bash
# 检查新OpenVPN容器是否正常运行
docker ps | grep 1Panel-openvpn-sNji
docker logs 1Panel-openvpn-sNji --tail 20

# 检查端口监听
netstat -tulpn | grep 1194
```

#### 5.2 连接功能验证
```bash
# 测试多个客户端同时连接（验证无连接数限制）
# 1. 分发.ovpn文件给测试用户
# 2. 让多个用户（超过2个）同时连接测试
# 3. 确认所有连接都能成功建立

# 检查连接状态
docker exec 1Panel-openvpn-sNji cat /tmp/openvpn_status.log
```

#### 5.3 网络路由验证
```bash
# 从VPN客户端测试访问1panel-network内的服务
# 例如测试访问mysql-prod, redis-prod等容器

# 检查容器网络
docker exec 1Panel-openvpn-sNji ip route
```

## 回滚计划

如果迁移出现问题，可以快速回滚：

```bash
# 1. 停止新的开源OpenVPN容器
docker stop 1Panel-openvpn-sNji

# 2. 恢复原来的docker-compose.yml
cd /opt/1panel/apps/openvpn/openvpn/
cp docker-compose.yml.backup docker-compose.yml

# 3. 启动原Access Server容器
docker compose up -d

# 4. 客户端恢复原有配置文件
```

## 重要结论

### 网络架构影响评估
经过详细的环境调研，发现迁移对现有网络架构的影响极小：

1. **1Panel容器网络无影响**
   - OpenVPN容器继续在1panel-network中运行
   - 其他容器间通信不受影响
   - 端口映射保持不变

2. **VPN访问模式无变化**
   - 服务器地址、端口保持不变
   - VPN客户端仍可访问**********/16网段内的所有容器服务
   - 路由配置可保持现有模式

3. **最大变化仅在客户端**
   - 需要重新分发客户端配置文件
   - 证书格式从Access Server改为标准OpenVPN格式

### 优化建议
基于当前容器化架构，建议采用**原地替换**策略：
- 构建兼容的开源OpenVPN Docker镜像
- 保持现有的docker-compose.yml结构
- 最小化配置变更，最大化兼容性

## 风险评估与缓解

### 技术风险
- **证书问题**：提前测试证书生成和验证
- **网络配置**：备份现有iptables规则
- **路由冲突**：仔细规划IP地址段

### 业务风险  
- **服务中断**：选择低峰时段执行
- **用户影响**：提前通知用户准备新配置
- **连接失败**：准备详细故障排除指南

### 缓解措施
- 完整备份现有配置
- 准备快速回滚方案
- 建立测试验证清单
- 提供用户技术支持

## 后续维护

### 日常管理任务
- 客户端证书生成和撤销
- 连接日志监控
- 证书到期管理
- 系统更新维护

### 监控指标
- 并发连接数
- 网络吞吐量
- 连接成功率
- 系统资源使用

通过此迁移计划，可以彻底解决OpenVPN并发连接限制问题，同时保持服务的安全性和稳定性。

## 实际执行记录

### 执行日期
2025-07-28 14:52 - 14:57

### 实际执行与计划差异

#### 差异1：PKI初始化方式
**计划**：使用交互式方式初始化PKI
```bash
docker run -v $(pwd)/data:/etc/openvpn --rm -it kylemanna/openvpn ovpn_initpki
```

**实际**：使用非交互式方式，避免TTY问题
```bash
docker run -v $(pwd)/data:/etc/openvpn --rm -e EASYRSA_BATCH=1 -e EASYRSA_REQ_CN="OpenVPN-CA" kylemanna/openvpn ovpn_initpki nopass
```

#### 差异2：配置文件生成位置
**计划**：在应用目录下生成配置文件
**实际**：在/tmp目录下生成，避免权限问题

#### 差异3：无需手动创建docker-compose.yml.new
**计划**：通过cat创建新文件
**实际**：使用sudo tee直接写入，更简洁

### 执行结果确认
- ✅ 服务正常运行：容器ID d0fdddcc54ed
- ✅ 网络配置正确：********** in 1panel-network  
- ✅ 端口映射正确：1194/udp
- ✅ VPN网段正确：************/24
- ✅ 路由配置正确：可访问**********/16
- ✅ 客户端配置文件成功生成4个

## 用户管理说明

### 1. 配置文件的使用方式

**问题**：同一个配置文件能否给多个人使用？
**答案**：**不能**。每个.ovpn配置文件包含独有的客户端私钥和证书，应该一人一个配置文件。

**配置文件区别**：
- 每个配置文件包含不同的客户端私钥（`<key>`部分）
- 每个配置文件包含不同的客户端证书（`<cert>`部分）
- 服务器地址、端口、CA证书等公共部分相同
- 每个证书都有唯一的序列号，服务器可以独立管理

**安全原因**：
- 如果多人共用一个配置文件，无法区分用户身份
- 一旦配置文件泄露，需要同时影响所有使用者
- 无法单独撤销某个用户的访问权限

### 2. 手动创建新用户配置文件

当需要为新用户创建配置时，按以下步骤执行：

#### 步骤1：生成客户端证书
```bash
cd /opt/1panel/apps/openvpn/openvpn
docker run -v $(pwd)/data:/etc/openvpn --rm kylemanna/openvpn easyrsa build-client-full [用户名] nopass
```

#### 步骤2：生成配置文件
```bash
cd /tmp  # 或其他有写权限的目录
docker run -v /opt/1panel/apps/openvpn/openvpn/data:/etc/openvpn --rm kylemanna/openvpn ovpn_getclient [用户名] > [用户名].ovpn
```

#### 示例：为"张三"创建配置
```bash
# 生成证书
cd /opt/1panel/apps/openvpn/openvpn
docker run -v $(pwd)/data:/etc/openvpn --rm kylemanna/openvpn easyrsa build-client-full zhangsan nopass

# 生成配置文件
cd /tmp
docker run -v /opt/1panel/apps/openvpn/openvpn/data:/etc/openvpn --rm kylemanna/openvpn ovpn_getclient zhangsan > zhangsan.ovpn
```

### 3. 用户管理操作

#### 查看所有已生成的客户端证书
```bash
cd /opt/1panel/apps/openvpn/openvpn
docker run -v $(pwd)/data:/etc/openvpn --rm kylemanna/openvpn ovpn_listclients
```

#### 撤销用户证书（禁用用户）
```bash
cd /opt/1panel/apps/openvpn/openvpn
docker run -v $(pwd)/data:/etc/openvpn --rm -it kylemanna/openvpn easyrsa revoke [用户名]
docker run -v $(pwd)/data:/etc/openvpn --rm kylemanna/openvpn easyrsa gen-crl
# 重启容器使撤销生效
docker restart 1Panel-openvpn-sNji
```

### 4. 当前已创建的用户
已为以下用户创建了配置文件：
- user1.ovpn
- user2.ovpn  
- user3.ovpn
- admin.ovpn

**配置文件位置**：已下载到本地迁移记录目录，可直接分发给对应用户使用。