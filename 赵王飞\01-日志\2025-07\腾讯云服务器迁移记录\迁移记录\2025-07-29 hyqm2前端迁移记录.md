# 2025-07-29 汇远轻媒2.0前端迁移记录

**迁移时间**: 2025-07-29  
**迁移内容**: 汇远轻媒2.0项目前端静态文件迁移  
**源环境**: 200.1.66.208 服务器  
**目标环境**: 腾讯云1Panel + OpenResty  

## 背景

汇远轻媒2.0项目之前已完成后端迁移，后端容器 `hyqm2-prod` 运行在端口9006。现需要迁移前端静态文件，实现前端静态文件服务 + API反向代理的混合架构。

## 核心技术方案

### 架构设计
```
外部请求 → 雷池WAF → 1Panel OpenResty → {
    ├── 静态文件请求 → 直接返回 (/www/sites/hyqm2.nmzyb.cn/index/)
    └── API请求 (/api/*) → 反向代理 → hyqm2-prod:9006
}
```

### 关键技术突破：1Panel自定义配置方式

**问题**: 1Panel反向代理模式只能配置纯代理，无法同时设置静态文件服务

**解决方案**: 利用1Panel配置文件包含机制，新建独立配置文件
- 1Panel主配置：`/opt/1panel/www/conf.d/hyqm2.nmzyb.cn.conf`
- 包含指令：`include /www/sites/hyqm2.nmzyb.cn/proxy/*.conf;`
- 现有API配置：`/opt/1panel/www/sites/hyqm2.nmzyb.cn/proxy/root.conf`
- **新建静态配置**：`/opt/1panel/www/sites/hyqm2.nmzyb.cn/proxy/static.conf`

**优势**:
- ✅ 不修改1Panel生成的配置，避免被覆盖
- ✅ 扩展性好，可以继续添加更多配置文件
- ✅ 与1Panel管理机制兼容

## 详细实施过程

### 步骤1：文件传输
```bash
# 208服务器打包
cd /usr/share/nginx/html && tar -czf hyqm2-frontend.tar.gz hyqm2.0
mv hyqm2-frontend.tar.gz /usr/share/nginx/html/transfer/

# 腾讯云下载
cd /tmp && wget https://main.51zsqc.com/transfer/hyqm2-frontend.tar.gz
```

### 步骤2：部署静态文件
```bash
# 创建目录
mkdir -p /opt/1panel/www/sites/hyqm2.nmzyb.cn/index

# 解压部署
tar -xzf hyqm2-frontend.tar.gz
sudo cp -r /tmp/hyqm2.0/* /opt/1panel/www/sites/hyqm2.nmzyb.cn/index/
```

### 步骤3：关键配置创建
创建文件：`/opt/1panel/www/sites/hyqm2.nmzyb.cn/proxy/static.conf`

```nginx
# 静态文件服务配置 - 汇远轻媒2.0前端
location / {
    root /www/sites/hyqm2.nmzyb.cn/index;
    index index.html index.htm;
    try_files $uri $uri/ /index.html;
    
    # 静态资源缓存配置
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # HTML文件不缓存，确保及时更新
    location ~* \.(html|htm)$ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
    }
}
```

**重要技术点**:
1. **容器路径映射**: 宿主机 `/opt/1panel/www` → 容器内 `/www`
2. **SPA路由支持**: `try_files $uri $uri/ /index.html`
3. **缓存策略优化**: HTML文件无缓存，静态资源1年缓存
4. **配置优先级**: location精确匹配优先于前缀匹配

### 步骤4：配置生效
```bash
# 测试配置语法
docker exec 1Panel-openresty-YFc6 openresty -t

# 重新加载配置
docker exec 1Panel-openresty-YFc6 openresty -s reload
```

### 步骤5：验证测试
```bash
# 前端页面测试
curl -I http://hyqm2.nmzyb.cn/
# HTTP/1.1 200 OK, Content-Length: 1288

# 静态资源测试  
curl -I http://hyqm2.nmzyb.cn/favicon.ico
# HTTP/1.1 200 OK, Expires: Wed, 29 Jul 2026

# API代理测试
curl -I http://hyqm2.nmzyb.cn/api/
# 正确代理到后端容器
```

## 最终配置文件结构

```
/opt/1panel/www/sites/hyqm2.nmzyb.cn/
├── index/                # 前端静态文件目录
│   ├── index.html
│   ├── assets/
│   ├── js/
│   └── ...
├── proxy/               # nginx配置文件目录
│   ├── root.conf       # API反向代理配置（1Panel自动生成）
│   └── static.conf     # 静态文件服务配置（手动创建）
├── log/                # 访问日志
└── ssl/                # SSL证书（如需要）
```

## 核心配置内容对比

### API反向代理配置 (root.conf)
```nginx
location ^~ /api/ {
    proxy_pass http://127.0.0.1:9006; 
    proxy_set_header Host $host; 
    proxy_set_header X-Real-IP $remote_addr; 
    # ... 其他代理头设置
}
```

### 静态文件服务配置 (static.conf)  
```nginx
location / {
    root /www/sites/hyqm2.nmzyb.cn/index;
    index index.html index.htm;
    try_files $uri $uri/ /index.html;
    # ... 缓存策略设置
}
```

## 技术原理说明

### Nginx Location匹配优先级
1. **精确匹配** (`=`) - 最高优先级
2. **前缀匹配** (`^~`) - API代理使用此方式
3. **正则匹配** (`~`) - 静态资源缓存使用
4. **普通前缀匹配** - 静态文件根路径使用

### 配置加载机制
1. 1Panel生成主配置文件，包含 `include /www/sites/域名/proxy/*.conf;`
2. OpenResty按文件名顺序加载proxy目录下所有.conf文件
3. `root.conf` (API配置) 和 `static.conf` (静态配置) 并行生效
4. 请求根据URL路径自动匹配对应的location块

## 性能优化配置

### 缓存策略
- **HTML文件**: 无缓存，确保及时更新
- **静态资源**: 1年缓存，减少请求次数
- **API请求**: 无缓存，确保数据实时性

### 访问日志优化
- 静态资源访问不记录日志，减少I/O开销
- HTML文件访问记录日志，便于分析

## 故障排查要点

### 常见问题及解决方法
1. **404错误**: 检查容器内路径映射是否正确
2. **配置不生效**: 确认nginx配置语法正确并重新加载
3. **API代理失败**: 检查后端容器状态和端口配置
4. **静态资源缓存问题**: 检查缓存头设置和浏览器缓存

### 调试命令
```bash
# 检查配置语法
docker exec 1Panel-openresty-YFc6 openresty -t

# 查看访问日志
tail -f /opt/1panel/www/sites/hyqm2.nmzyb.cn/log/access.log

# 测试容器内文件访问
docker exec 1Panel-openresty-YFc6 ls -la /www/sites/hyqm2.nmzyb.cn/index/

# 测试端口连通性
curl -I http://hyqm2.nmzyb.cn/
curl -I http://hyqm2.nmzyb.cn/api/
```

## 清理工作

### 临时文件清理
```bash
# 208服务器清理
rm /usr/share/nginx/html/transfer/hyqm2-frontend.tar.gz

# 腾讯云服务器清理  
rm /tmp/hyqm2-frontend.tar.gz
rm -rf /tmp/hyqm2.0
```

## 迁移结果

**✅ 成功实现目标**:
- 前端静态文件正常访问：`http://hyqm2.nmzyb.cn/`
- API接口正常代理：`http://hyqm2.nmzyb.cn/api/*`
- 缓存策略优化，性能提升
- 配置灵活扩展，维护简单

**✅ 核心技术创新**:
- 突破1Panel配置限制，实现静态+代理混合服务
- 利用配置文件包含机制，避免配置被覆盖
- 优化缓存策略，提升访问性能

## 后续维护建议

1. **配置备份**: 定期备份 `/opt/1panel/www/sites/hyqm2.nmzyb.cn/proxy/static.conf`
2. **监控配置**: 关注1Panel更新是否影响自定义配置
3. **性能监控**: 定期检查访问日志和响应时间
4. **安全加固**: 根据需要添加安全头和访问控制规则

---

**总结**: 本次迁移成功解决了1Panel环境下前端静态文件与API代理共存的技术难题，为后续类似项目迁移提供了可复用的技术方案。