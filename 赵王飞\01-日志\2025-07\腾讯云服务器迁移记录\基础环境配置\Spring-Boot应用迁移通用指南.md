# Spring Boot应用容器化迁移通用指南

## 🚨 重要原则：理解而不是复制

每个Spring Boot应用都是独特的，盲目复制其他项目的配置会导致严重问题。

## 📋 迁移前必查清单

### 1. 识别应用特征
```bash
# 查看jar文件名
ls -la /workdir/*.jar

# 查看可用的profile
unzip -l [jar文件名] | grep "application-.*\.yml"

# 查看主配置文件
unzip -p [jar文件名] BOOT-INF/classes/application.yml | head -50
```

### 2. 理解配置层次
- application.yml - 基础配置
- application-{profile}.yml - 环境特定配置
- 命令行参数 - 最高优先级覆盖

## ⚠️ 常见错误模式

### ❌ 错误方式：环境变量地狱
```yaml
# 不要这样做！
environment:
  - SPRING_REDIS_HOST=redis-prod
  - SPRING_REDIS_PORT=6379
  - SPRING_DATASOURCE_DRUID_INITIALSIZE=5
  - SPRING_DATASOURCE_DRUID_MINIDLE=10
  - SPRING_DATASOURCE_DRUID_MAXACTIVE=20
  - SPRING_DATASOURCE_DRUID_MAXWAIT=60000
  - SPRING_DATASOURCE_DRUID_CONNECTTIMEOUT=30000
  # ... 无穷无尽的配置
```

**症状**：
- 不断报错"Could not resolve placeholder"
- 需要添加越来越多的环境变量
- 配置参数似乎永远不够

### ✅ 正确方式：命令行参数覆盖
```yaml
environment:
  - COMMAND_NAME=-jar [jar名] --spring.profiles.active=[profile] --spring.redis.host=redis-prod --spring.redis.port=6379 --spring.rabbitmq.host=rabbitmq-prod
```

**原则**：只覆盖连接信息，不覆盖所有配置

## 🔍 配置覆盖决策树

```
需要修改配置？
├─ 是连接信息（数据库/Redis/MQ）？
│  └─ 使用命令行参数 --spring.xxx=yyy
├─ 是少量配置（<5个）？
│  └─ 可以使用环境变量
└─ 是大量配置？
   └─ ⚠️ 停下来！检查是否用错了profile
```

## 📝 标准迁移流程

### 步骤1：分析应用
```bash
# 1.1 确认jar文件
ls -la workdir/*.jar

# 1.2 查看所有profile
unzip -l xxx.jar | grep "application-.*\.yml"

# 1.3 查看特定profile内容
unzip -p xxx.jar BOOT-INF/classes/application-prod.yml
```

### 步骤2：确定启动参数
```yaml
# 基础模板
COMMAND_NAME=-jar [jar文件名] --spring.profiles.active=[环境] --ruoyi.profile=/workdir/upload

# 必要的连接覆盖
--spring.redis.host=redis-prod
--spring.redis.port=6379
--spring.rabbitmq.host=rabbitmq-prod
--spring.rabbitmq.port=5672
--spring.datasource.xxx.url=***********************/[数据库名]
```

### 步骤3：验证配置
1. Redis连接：查找 "connections initialized for redis-prod"
2. 数据库连接：不应该有旧IP地址
3. 应用启动：查找 "Started" 或 "项目启动成功"

## 🎯 判断迁移策略

### 场景1：简单应用
- 有明确的prod/dev profile
- 配置结构清晰
- **策略**：使用对应profile + 少量覆盖

### 场景2：复杂应用
- 多个自定义profile
- 大量自定义配置
- **策略**：可能需要创建新的配置文件

### 场景3：特殊框架
- 使用特殊启动器（如yudao）
- 自定义配置加载机制
- **策略**：深入分析原始运行方式

## 💡 调试技巧

### 1. 配置加载顺序
```
默认值 < application.yml < application-{profile}.yml < 环境变量 < 命令行参数
```

### 2. 常见错误对照
| 错误信息 | 可能原因 | 解决方法 |
|---------|---------|---------|
| Could not resolve placeholder | 缺少配置项 | 检查是否用对profile |
| Access denied for user | 数据库认证失败 | 检查用户名密码和权限 |
| Connection refused | 服务地址错误 | 检查容器名称解析 |

## 📌 核心认知

1. **每个应用都不同** - 不存在通用配置
2. **Profile很重要** - 错误的profile会导致大量配置缺失
3. **覆盖要适度** - 只改必要的连接信息
4. **理解优于复制** - 理解配置机制比复制配置重要

## 🔧 实用命令集

```bash
# 查看容器内Java进程参数
docker exec [容器名] ps aux | grep java

# 实时查看启动日志
docker logs -f [容器名]

# 查看Spring Boot激活的profile
docker logs [容器名] | grep "The following.*profile"

# 检查数据库连接
docker logs [容器名] | grep -E "(jdbc|mysql|Connection)"

# 检查Redis连接
docker logs [容器名] | grep -E "(redis|Redis)"
```

记住：成功的迁移基于理解，而不是模仿！