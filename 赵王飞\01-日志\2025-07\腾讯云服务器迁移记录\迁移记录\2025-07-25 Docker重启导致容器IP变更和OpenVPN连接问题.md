---
date_created: 2025-07-25 19:20:00
date_modified: 2025-07-25 19:20:00
author: 赵王飞
---

# 背景

在解决雷池申请证书DNS问题时，执行了`sudo systemctl restart docker`重启Docker服务，导致：
1. 所有容器的IP地址发生变更
2. OpenVPN客户端无法连接，报错`Connection reset by peer (WSAECONNRESET)`

# 问题分析

## 1. 容器IP变更原因

Docker服务重启时的行为：
- **网络重建**：所有Docker管理的网络（bridge、自定义网络等）都会被重建
- **IP重新分配**：容器重启后，Docker的IPAM（IP地址管理）会重新分配IP地址
- **无IP保证**：Docker使用内部DHCP机制，不保证容器重启后获得相同IP

这是Docker的正常行为，除非在创建容器时指定了固定IP。

## 2. OpenVPN连接失败分析

错误日志显示：
```
read UDP: Connection reset by peer (WSAECONNRESET) (code=10054)
Server poll timeout, restarting
```

原因：Docker重启后，虽然OpenVPN容器自动启动，但可能存在初始化问题或iptables规则未正确加载。

# 解决过程

## 1. 检查OpenVPN服务状态
```bash
# 查找OpenVPN容器
docker ps | grep -i vpn
# 输出：1Panel-openvpn-sNji

# 检查容器日志
docker logs 1Panel-openvpn-sNji --tail 30
# 显示服务已启动

# 检查端口监听
ss -tlnpu | grep -E "1194|943|7443"
# 确认端口正在监听
```

## 2. 检查防火墙规则
```bash
# 检查iptables规则
sudo iptables -L -n | grep -A 5 -B 5 "1194"
# 发现OpenVPN容器IP为**********，规则存在
```

## 3. 重启OpenVPN容器
```bash
# 重启容器
docker restart 1Panel-openvpn-sNji

# 等待启动完成
sleep 10

# 验证端口可达
nc -zvu *************** 1194
# 输出：Connection to *************** 1194 port [udp/openvpn] succeeded!
```

# 解决结果

1. **容器IP变更**：这是Docker重启的正常行为，建议使用容器名称而非IP进行服务间通信
2. **OpenVPN恢复**：通过重启OpenVPN容器解决了连接问题，服务恢复正常

# 经验总结

1. **避免频繁重启Docker**：会导致所有容器IP变更，影响服务间通信
2. **使用容器名称**：在配置中使用容器名称而非IP地址，避免IP变更影响
3. **服务依赖检查**：重启Docker后，需要检查所有关键服务是否正常运行
4. **OpenVPN特殊处理**：OpenVPN容器在Docker重启后可能需要手动重启才能正常工作