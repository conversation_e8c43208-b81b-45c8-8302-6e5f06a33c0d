# 2025-07-29 掌上青城CLI业务迁移实施记录

## 迁移概览

**项目**: 掌上青城CLI业务迁移  
**时间**: 2025-07-29  
**源环境**: 208服务器K8s集群  
**目标环境**: 腾讯云Docker Compose环境  

## 迁移服务清单

| 服务名称 | 功能描述 | 启动命令 | 原状态 | 迁移状态 |
|---------|---------|----------|--------|----------|
| calgps-product | GPS计算服务 | `think CalGps -E yun -L error` | 运行中 | 🔄 待迁移 |
| process-gps-product | GPS数据处理服务 | `think processGPS -E yun -L error` | 运行中 | 🔄 待迁移 |
| process-stn-product | 站点数据处理服务 | `think processSTN -E yun` | 运行中 | 🔄 待迁移 |
| zhxclient-product | 中航讯数据接收端 | `think zhxClient -E yun` | 代码存在但未运行 | 🔄 待迁移 |

## 数据库迁移清单

| 数据库名称 | 源服务器 | 用途 | 迁移状态 |
|-----------|---------|------|----------|
| zsgj | 200.1.66.214:3306 | 主业务数据库 | 🔄 待迁移 |
| zsqc_cal_data | 200.1.66.214:3306 | 计算数据库 | 🔄 待迁移 |
| real | ************:30012 | 实时数据库 | 🔄 待迁移 |
| zsgj_gps_data | ************:30012 | GPS数据库 | 🔄 待迁移 |

## 实施步骤记录

### Phase 1: 环境准备
**开始时间**: 2025-07-29 
**状态**: 🔄 进行中

#### 1.1 创建目标目录结构
**时间**: 2025-07-29 08:21
**操作**: 
- 创建应用配置目录: `/mnt/datadisk0/apps/php-cli/zsgj-cli/`
- 创建数据存储目录: `/mnt/datadisk0/volumns/php-cli/zsgj-cli/`
- 创建子目录: conf, logs, scripts, code

**结果**: ✅ 成功创建所有目录结构

#### 1.2 分析多层级环境配置结构
**时间**: 2025-07-29 08:45
**操作**: 
- 发现应用具有**多层级环境配置**结构
- 全局环境: `/env/tencent.php` 
- 模块环境: `/application/{模块}/env/tencent.php`
- 关键模块需要独立配置: calculationGps, zsqcDataProcess

**结果**: ✅ 确认需要创建多层级配置文件

#### 1.3 制定数据库迁移策略
**时间**: 2025-07-29 08:50
**操作**: 
- 分析zsgj_gps_data数据库结构，发现125个表
- **重要发现**: 大量按日期命名的表(如analysis_result_20250602)为历史数据
- **迁移策略**: 只迁移核心业务表，排除所有日期格式命名的表

**结果**: ✅ 避免迁移数TB的历史GPS数据

### Phase 2: 应用代码迁移
**开始时间**: 
**状态**: 🔄 待开始

#### 2.1 打包源代码
**时间**: 
**操作**: 从NFS存储 `/data/nfs_share/cli/cli_main` 打包应用代码
**命令**: 
```bash
# 在208服务器执行
tar -czf zsgj-cli-code.tar.gz -C /data/nfs_share/cli cli_main
mv zsgj-cli-code.tar.gz /usr/share/nginx/html/transfer/
```
**结果**: 

#### 2.2 传输到腾讯云
**时间**: 
**操作**: 通过HTTP直传方式下载代码包
**命令**: 
```bash
# 在腾讯云服务器执行
cd /mnt/datadisk0/volumns/php-cli/zsgj-cli/
wget https://main.51zsqc.com/transfer/zsgj-cli-code.tar.gz
tar -xzf zsgj-cli-code.tar.gz
```
**结果**: 

#### 2.3 配置文件适配
**时间**: 
**操作**: 创建腾讯云环境配置文件
**结果**: 

### Phase 3: 数据库迁移
**开始时间**: 
**状态**: 🔄 待开始

#### 3.1 导出源数据库
**时间**: 
**操作**: 导出各个数据库
**命令**: 
```bash
# zsgj数据库
mysqldump -h 200.1.66.214 -u root -p'TxkjDB2020#' --single-transaction --routines --triggers zsgj > zsgj.sql

# zsqc_cal_data数据库
mysqldump -h 200.1.66.214 -u root -p'TxkjDB2020#' --single-transaction --routines --triggers zsqc_cal_data > zsqc_cal_data.sql

# real数据库
mysqldump -h ************ -P 30012 -u root -p'FKPMuj2kQ7' --single-transaction --routines --triggers real > real.sql

# zsgj_gps_data数据库
mysqldump -h ************ -P 30012 -u root -p'FKPMuj2kQ7' --single-transaction --routines --triggers zsgj_gps_data > zsgj_gps_data.sql
```
**结果**: 

#### 3.2 传输数据库文件
**时间**: 
**操作**: 通过HTTP直传方式传输数据库文件
**结果**: 

#### 3.3 导入到目标数据库
**时间**: 
**操作**: 导入到mysql-prod容器
**结果**: 

### Phase 4: 容器化部署
**开始时间**: 
**状态**: 🔄 待开始

#### 4.1 创建Docker Compose配置
**时间**: 
**操作**: 创建服务编排配置
**结果**: 

#### 4.2 启动服务容器
**时间**: 
**操作**: 启动各个CLI服务
**结果**: 

### Phase 5: 功能验证
**开始时间**: 
**状态**: 🔄 待开始

#### 5.1 验证服务启动
**时间**: 
**操作**: 检查各个容器运行状态
**结果**: 

#### 5.2 验证数据库连接
**时间**: 
**操作**: 测试应用连接数据库
**结果**: 

#### 5.3 验证业务功能
**时间**: 
**操作**: 验证GPS计算、数据处理等核心功能
**结果**: 

## 遇到的问题和解决方案

### 问题1: hyqm2-prod容器进程定时重启
**描述**: 
- 发现hyqm2-prod容器中supervisor管理的所有进程每天凌晨2点被SIGHUP信号中断并重启
- 日志显示: `WARN exited: [进程名] (terminated by SIGHUP; not expected)`
- 影响进程包括: django, celery-beat, 各类RPC服务, 爬虫执行器等

**影响评估**:
- 服务中断时间: 1-2秒（supervisor自动重启）
- 发生时间: 凌晨2:00（业务低峰期）
- 业务影响: 较小（有重试和容错机制）

**初步分析**:
- 可能是日志轮转导致的SIGHUP信号
- 也可能是定时任务触发的进程重载
- 需要进一步调查具体原因

**解决方案**: 
- 暂时记录此问题，不影响业务运行
- 后续如需深入调查，可检查:
  - 宿主机定时任务
  - logrotate配置
  - supervisor日志轮转设置
  - Django/Celery定时任务配置

**结果**: ⏸️ 暂缓处理（影响较小，待后续优化）

### 问题2: 数据库用户名密码配置问题
**描述**: 
- 初始配置使用了pearproject用户，但该用户不适合掌上青城CLI业务
- 需要使用专门的数据库用户凭证
- 正确的用户信息：`zsqc` / `Zsqc@2025#Secure`

**解决方案**: 
- 更新所有环境配置文件中的数据库用户名和密码
- 确保全局配置和模块配置都使用正确的凭证

**结果**: 🔄 正在更新配置文件

## 配置文件记录

### 腾讯云环境配置 (tencent.php)
```php
<?php
print "加载腾讯云环境配置文件\n";

// Redis配置
const REDIS_30102 = [
    'type' => 'redis',
    'host' => "redis-prod",
    'port' => 6379,
];
const REDIS_PRODUCT = [
    'type' => 'redis',
    'host' => "redis-prod", 
    'port' => 6379,
];

// Memcache配置
const MEMCACHE_PRODUCT = [
    'type' => 'memcache',
    'host' => "memcached-prod",
    'port' => 11211,
];
const MEMCACHE_30202 = [
    'type' => 'memcache',  
    'host' => "memcached-prod",
    'port' => 11211,
];

// 主数据库配置
const DB_PRODUCT_HOST = [
    'type' => 'mysql',
    'hostname' => 'mysql-prod',
    'username' => 'zsqc',
    'password' => 'Zsqc@2025#Secure',
    'hostport' => '3306',
];

// GPS数据库配置
const DB_ZSGJ_GPS_DATA_GPS = [
    'type' => 'mysql',
    'hostname' => 'mysql-prod',
    'database' => 'zsgj_gps_data',
    'username' => 'zsqc',
    'password' => 'Zsqc@2025#Secure',
    'hostport' => '3306',
];

// 实时数据库配置
define("DB_REAL", [
    'type' => 'mysql',
    'hostname' => 'mysql-prod',
    'database' => 'real',
    'username' => 'zsqc', 
    'password' => 'Zsqc@2025#Secure',
    'hostport' => '3306',
]);

// 计算数据库配置
const DB_ZSQC_CAL_DATA_GPS = [
    'type' => 'mysql',
    'hostname' => 'mysql-prod',
    'database' => 'zsqc_cal_data',
    'username' => 'zsqc',
    'password' => 'Zsqc@2025#Secure', 
    'hostport' => '3306',
];

// 主业务数据库配置
const DB_ZSGJ = [
    'type' => 'mysql',
    'hostname' => 'mysql-prod',
    'database' => 'zsgj',
    'username' => 'zsqc',
    'password' => 'Zsqc@2025#Secure',
    'hostport' => '3306',
];

// 异常监控Redis配置
const REDIS_ABNORMAL_MONITORING = REDIS_PRODUCT;
```

### 应用层级环境配置

#### calculationGps模块配置 (application/calculationGps/env/tencent.php)
```php
<?php
// GPS计算模块腾讯云环境配置

// 计算数据库
const DB_HOST_ZSQC_CAL_DATA = "mysql-prod";
const DB_USER_ZSQC_CAL_DATA = "zsqc";
const DB_PORT_ZSQC_CAL_DATA = "3306";
const DB_NAME_ZSQC_CAL_DATA = "zsqc_cal_data";
const DB_PSW_ZSQC_CAL_DATA = "Zsqc@2025#Secure";

// 主业务数据库
const DB_HOST_ZSGJ = "mysql-prod";
const DB_USER_ZSGJ = "zsqc";
const DB_PORT_ZSGJ = "3306";
const DB_NAME_ZSGJ = "zsgj";
const DB_PSW_ZSGJ = "Zsqc@2025#Secure";

// GPS数据库
const DB_HOST_ZSGJ_GPS_DATA = "mysql-prod";
const DB_USER_ZSGJ_GPS_DATA = "zsqc";
const DB_PORT_ZSGJ_GPS_DATA = "3306";
const DB_NAME_ZSGJ_GPS_DATA = "zsgj_gps_data";
const DB_PSW_ZSGJ_GPS_DATA = "Zsqc@2025#Secure";

// Redis缓存
const PRODUCT_REDIS_HOST = "redis-prod";
const PRODUCT_REDIS_PORT = "6379";

// Memcache缓存
const PRODUCT_MEMCACHE_HOST = "memcached-prod";
const PRODUCT_MEMCACHE_PORT = "11211";

define("IS_DEBUG", false);
const IS_DATA_STORAGE = false;
const IS_MONITOR = false;
```

#### zsqcDataProcess模块配置 (application/zsqcDataProcess/env/tencent.php)
```php
<?php
// 数据处理模块腾讯云环境配置

// 中航讯服务(保持不变)
const RECEIVE_CONFIG_ZHX = 'tcp://***************:8686';

// Memcache配置
const MEMCACHE_IP = "memcached-prod";
const MEMCACHE_PORT = 11211;
const REALMAP_MEMCACHE_IP = "memcached-prod";
const REALMAP_MEMCACHE_PORT = 11211;

// Redis配置
const REDIS_IP = "redis-prod";
const REDIS_PORT = "6379";
const REDIS_PASS = "";

// 主业务数据库配置
const DB_ZSGJ_HOST = "mysql-prod";
const DB_ZSGJ_PORT = 3306;
const DB_ZSGJ_USER = "zsqc";
const DB_ZSGJ_PSW = "Zsqc@2025#Secure";

// GPS数据库配置
const DB_ZSGJ_GPS_DATA_HOST = "mysql-prod";
const DB_ZSGJ_GPS_DATA_NAME = 'zsgj_gps_data';
const DB_ZSGJ_GPS_DATA_USER = 'zsqc';
const DB_ZSGJ_GPS_DATA_PWD = 'Zsqc@2025#Secure';
const DB_ZSGJ_GPS_DATA_PORT = '3306';
```

### Docker Compose配置
```yaml
# 将在实施过程中创建
```

## 总结

**迁移状态**: 🔄 进行中  
**开始时间**: 2025-07-29  
**预计完成时间**: 待定  

### 成功要点
- 

### 注意事项
- 本迁移涉及关键业务数据，已做好数据备份
- 迁移过程中密切监控服务状态
- 遇到问题及时记录解决方案

---
*本记录将在迁移过程中持续更新*