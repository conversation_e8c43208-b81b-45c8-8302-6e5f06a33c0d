# 1Panel目录结构与管理机制详解

**创建时间**: 2025-07-28 10:45  
**更新时间**: 2025-07-29  
**适用场景**: 腾讯云服务器雷池WAF + 1Panel OpenResty + 容器架构

## 📋 整体架构说明

### 请求链路
```
外部请求 → 雷池WAF → 1Panel OpenResty → 容器应用
```

### 管理原则
- **唯一创建方式**: 站点只能通过1Panel前端管理界面创建，不支持命令行直接创建
- **自动化管理**: 1Panel自动维护nginx配置和目录结构
- **配置同步**: 前端操作会自动更新后端配置文件和目录结构

## 📁 1Panel站点目录结构

### 标准目录结构
```
/opt/1panel/www/sites/[域名]/
├── index/          # 静态文件目录（如果有）
├── log/            # 访问日志和错误日志
│   ├── access.log
│   └── error.log
├── proxy/          # 反向代理配置
│   └── root.conf   # 主要代理配置文件
└── ssl/            # SSL证书文件（如果启用HTTPS）
```

### 配置文件说明
- **proxy/root.conf**: OpenResty反向代理配置，定义如何转发请求到后端容器
- **log/**: 自动生成的访问日志和错误日志
- **ssl/**: SSL证书存放目录（HTTPS站点）
- **index/**: 静态文件目录（通常为空，因为是代理模式）

## 🔧 代理配置模板

### 标准反向代理配置
```nginx
location ^~ / {
    proxy_pass http://[容器IP]:[容器端口]; 
    proxy_set_header Host $host; 
    proxy_set_header X-Real-IP $remote_addr; 
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for; 
    proxy_set_header REMOTE-HOST $remote_addr; 
    proxy_set_header Upgrade $http_upgrade; 
    proxy_set_header Connection $http_connection; 
    proxy_set_header X-Forwarded-Proto $scheme; 
    proxy_set_header X-Forwarded-Port $server_port; 
    proxy_http_version 1.1; 
    add_header X-Cache $upstream_cache_status; 
    add_header Cache-Control no-cache; 
    proxy_ssl_server_name off; 
    proxy_ssl_name $proxy_host; 
}
```

### 参数说明
- **proxy_pass**: 后端容器的实际地址和端口
- **Host**: 保持原始Host头，确保应用接收到正确的域名
- **X-Real-IP**: 客户端真实IP地址
- **X-Forwarded-For**: 代理链中的IP地址
- **X-Forwarded-Proto**: 原始协议（http/https）

## 🔧 1Panel自动生成文件说明

### nginx主配置结构
```
/opt/1panel/www/
├── conf.d/              # nginx虚拟主机配置目录
│   ├── [域名].conf      # 每个站点的主配置文件
│   └── default.conf     # 默认配置文件
├── sites/               # 站点文件目录
│   └── [域名]/          # 具体站点目录
└── ssl/                 # SSL证书存储目录
```

### 站点创建后自动生成内容

#### 1. nginx配置文件 (/opt/1panel/www/conf.d/[域名].conf)
```nginx
server {
    listen 180 ; 
    server_name [域名]; 
    index index.php index.html index.htm default.php default.htm default.html; 
    
    # 代理头设置
    proxy_set_header Host $host; 
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for; 
    proxy_set_header X-Forwarded-Host $server_name; 
    proxy_set_header X-Real-IP $remote_addr; 
    proxy_http_version 1.1; 
    proxy_set_header Upgrade $http_upgrade; 
    proxy_set_header Connection "upgrade"; 
    
    # 日志配置
    access_log /opt/1panel/www/sites/[域名]/log/access.log main; 
    error_log /opt/1panel/www/sites/[域名]/log/error.log; 
    
    # 安全配置
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md) {
        return 404; 
    }
    
    # Let's Encrypt证书验证
    location ^~ /.well-known {
        allow all; 
        root /usr/share/nginx/html; 
    }
    
    # 引入代理配置
    include /opt/1panel/www/sites/[域名]/proxy/*.conf; 
}
```

#### 2. 代理配置文件 (/opt/1panel/www/sites/[域名]/proxy/root.conf)
通过1Panel前端界面设置的反向代理规则会自动写入此文件。

### 配置文件特点
- **自动维护**: 通过1Panel前端的任何修改都会自动更新这些配置文件
- **模板化**: 每个站点使用统一的配置模板
- **权限管理**: 1Panel自动设置正确的文件权限
- **配置验证**: 1Panel会自动验证nginx配置语法的正确性

## 📝 正确的站点管理流程

### 创建站点
1. **登录1Panel管理面板** (通常是 https://服务器IP:端口)
2. **进入"网站"管理模块**
3. **点击"创建网站"**
4. **选择"反向代理"类型**
5. **填写域名信息**
6. **配置代理目标** (容器名称和端口)
7. **保存配置** - 系统自动生成所有必要文件

### 修改站点配置
1. **在1Panel中找到对应站点**
2. **点击"设置"或"编辑"**
3. **修改代理配置、SSL设置等**
4. **保存更改** - 配置自动生效

### 删除站点
1. **在1Panel中选择要删除的站点**
2. **点击删除** - 系统自动清理所有相关文件和配置

## ⚠️ 重要注意事项

### 禁止的操作
- **禁止手动创建配置文件**: 直接创建的配置文件不会被1Panel识别
- **禁止直接编辑配置**: 手动修改配置文件可能被1Panel覆盖
- **禁止使用命令行nginx操作**: 可能与1Panel管理冲突

### 推荐的操作
- **统一使用1Panel界面管理**: 确保配置一致性和可维护性
- **备份重要配置**: 在进行重大变更前先备份配置
- **逐步验证**: 每次配置变更后验证站点是否正常访问