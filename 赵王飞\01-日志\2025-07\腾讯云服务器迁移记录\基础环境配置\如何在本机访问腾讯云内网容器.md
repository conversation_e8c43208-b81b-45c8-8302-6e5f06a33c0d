---
date_created: 2025-07-28 13:36:49
date_modified: 2025-07-28 14:00:00
author: 赵王飞
---

# 背景

目前，我们在服务端使用了 docker用来创建容器，容器之间互相访问是使用的是 docker 的名称，因为安全原因很多服务都没有对外暴露端口，但是我们在本地调试开发时，有时需要连接服务器例如 mysql、redis、rabbitmq 等等，我们的配置文件中也会配置他们的名称mysql-prod, 我们现在搭建了一个 docker openVPN 服务，使我们能够访问到1panel网络对应的容器（只能访问到这个docker网络）, 但是容器的 IP 被删除，重建后可能会变化,  如果指定 IP, 那么就有可能，某一天原本是正式的 mysql，突然变成了测试的 mysql, 而且我们使用 IP就无法使用正式环境的配置文件。在本地是无法通过 docker 名称直接访问到 docker 网络的。

为了解决这个问题，尝试过搭建一个 dns 解析服务，但是面临种种问题，最终没有搭建成功，最后的解决方案是，通过一个脚本用来生成1panel的网络的网络的容器名称与 IP 地址映射, 开发人员调用脚本定期手动将名称映射配置到 hosts。

# get_container_hosts.sh 脚本说明

## 脚本功能
该脚本用于自动生成腾讯云服务器上所有Docker容器的名称和IP地址映射，方便开发人员在本地通过容器名称访问内网服务。

## 脚本内容
```bash
#!/bin/bash
# 获取所有正在运行的容器的名称和IP地址

echo "# Docker container hosts - Generated on $(date)"
docker ps -q | xargs -r docker inspect --format '{{.Name}} {{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' | sed 's#^/##' | awk '{if ($2) print $2"\t"$1}'
```

## 脚本输出示例
```
# Docker container hosts - Generated on Mon Jul 28 01:43:26 PM CST 2025
***********	hyqm
***********	ruoyi-hyrm
***********	mysql-prod
***********	redis-prod
***********	rabbitmq-prod
# ... 更多容器映射
```

# Windows环境使用方法

## 前提条件
1. 已连接VPN到腾讯云内网
2. 具有腾讯云服务器SSH访问权限
3. Windows 10/11系统（支持PowerShell）

## 使用步骤

### 步骤1：获取容器hosts映射
通过SSH连接到腾讯云服务器并执行脚本：

```powershell
# 方式1：直接SSH执行（推荐）
ssh ubuntu@[腾讯云服务器IP] "bash ~/get_container_hosts.sh"

# 方式2：登录服务器后执行
ssh ubuntu@[腾讯云服务器IP]
bash ~/get_container_hosts.sh
```

### 步骤2：复制输出内容
将脚本输出的内容复制到剪贴板，格式如：
```
***********	mysql-prod
***********	redis-prod
***********	rabbitmq-prod
```

### 步骤3：修改Windows hosts文件

#### 方法A：使用记事本（管理员权限）
1. 按`Win + R`，输入`notepad`
2. 右键选择"以管理员身份运行"
3. 在记事本中打开文件：``
4. 在文件末尾添加复制的容器映射内容
5. 保存文件

#### 方法B：使用PowerShell（推荐）
以管理员身份打开PowerShell，执行：

```powershell
# 备份原hosts文件
Copy-Item "C:\Windows\System32\drivers\etc\hosts" "C:\Windows\System32\drivers\etc\hosts.backup"

# 添加容器映射（将下面的内容替换为实际的脚本输出）
$hostsContent = @"
# Docker container hosts - Updated on $(Get-Date)
***********	mysql-prod
***********	redis-prod
***********	rabbitmq-prod
# 添加其他容器映射...
"@

# 清理旧的Docker容器映射
$hostsFile = "C:\Windows\System32\drivers\etc\hosts"
(Get-Content $hostsFile) | Where-Object { $_ -notmatch "# Docker container hosts" -and $_ -notmatch "mysql-prod|redis-prod|rabbitmq-prod" } | Set-Content $hostsFile

# 添加新的映射
Add-Content -Path $hostsFile -Value $hostsContent
```

### 步骤4：验证配置
打开命令提示符或PowerShell测试连接：

```powershell
# 测试ping连通性
ping mysql-prod
ping redis-prod
ping rabbitmq-prod

# 测试端口连接（如果开放了端口）
telnet mysql-prod 3306
telnet redis-prod 6379
```

## 使用场景

### 开发环境配置
在本地项目配置文件中，可以直接使用容器名称：

```yaml
# application.yml
spring:
  datasource:
    url: ******************************************
  redis:
    host: mysql-prod
    port: 6379
  rabbitmq:
    host: rabbitmq-prod
    port: 5672
```

### 数据库连接工具
在Navicat、DBeaver等工具中，可以直接使用容器名称作为主机名：
- 主机名：`mysql-prod`
- 端口：`3306`
- 用户名：`pearproject`
- 密码：`kG7#tPq9@zR2$vX1`

## 注意事项

### ⚠️ 重要提醒
1. **容器IP会变化**：容器重启后IP地址可能发生变化，需要重新获取映射
2. **定期更新**：建议每天或容器重启后更新一次hosts映射
3. **管理员权限**：修改hosts文件需要管理员权限
4. **VPN连接**：确保VPN连接正常，否则无法访问内网IP

### 🔄 定期更新建议
建议团队成员：
- 每日开发前执行一次脚本更新hosts
- 容器重启后立即更新hosts映射
- 遇到连接异常时首先检查hosts配置

### 📝 批处理脚本（可选）
创建一个批处理文件自动化更新process：

```batch
@echo off
echo 正在获取容器hosts映射...
ssh ubuntu@[腾讯云服务器IP] "bash ~/get_container_hosts.sh" > container_hosts.txt
echo 请手动将container_hosts.txt中的内容添加到hosts文件
pause
```

这个解决方案虽然需要手动操作，但能有效解决在Windows开发环境中通过容器名称访问腾讯云内网服务的问题。

