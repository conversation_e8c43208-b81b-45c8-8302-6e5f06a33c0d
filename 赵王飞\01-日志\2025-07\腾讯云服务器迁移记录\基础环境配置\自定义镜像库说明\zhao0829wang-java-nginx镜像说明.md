# zhao0829wang/java-nginx:1.0 镜像说明文档

**创建时间**: 2025-07-25 21:30
**分析来源**: hyrm-imppc容器实际运行环境

## 📋 镜像概述

zhao0829wang/java-nginx:1.0是一个集成了Nginx和Java环境的Docker镜像，专门用于部署前后端分离的Spring Boot + Vue.js应用。该镜像在一个容器内同时运行Web服务器和Java应用服务器。

## 🏗️ 架构特点

### 核心组件
- **Nginx**: Web服务器，负责静态文件服务和反向代理
- **Java**: OpenJDK环境，运行Spring Boot应用
- **集成启动**: 通过entrypoint脚本同时启动两个服务

### 运行机制
```bash
# 启动脚本 /opt/entrypoint.sh
#!/bin/bash
nginx &           # 后台启动nginx
java $COMMAND_NAME # 前台启动Java应用
```

## 📁 目录结构

### 关键目录
```
/
├── opt/
│   └── entrypoint.sh          # 容器启动脚本
├── workdir/                   # Java应用目录
│   └── yudao-server.jar       # Spring Boot应用JAR
├── home/ruoyi/projects/
│   └── ruoyi-ui/              # Vue前端静态文件目录
└── etc/nginx/
    └── nginx.conf             # Nginx配置文件
```

### 挂载点规范
```yaml
volumes:
  - ./workdir:/workdir                    # Java应用文件
  - ./ui:/home/<USER>/projects/ruoyi-ui   # 前端静态文件
  - ./nginx.conf:/etc/nginx/nginx.conf   # 自定义nginx配置
  - ./logs:/home/<USER>/logs              # 应用日志
```

## ⚙️ 配置模式

### Nginx配置模板
```nginx
worker_processes  1;
events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;
    
    # 开启gzip压缩
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 5;
    gzip_types text/plain application/x-javascript text/css application/xml application/javascript;
    
    client_max_body_size 200m;

    server {
        listen       80;
        server_name  localhost;

        # 前端静态资源
        location / {
            root   /home/<USER>/projects/ruoyi-ui;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        # 后端API代理
        location /prod-api/ {
            proxy_pass http://localhost:48080/;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location /admin-api/ {
            proxy_pass http://localhost:48080/admin-api/;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location /app-api/ {
            proxy_pass http://localhost:48080/app-api/;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
}
```

### Java应用启动
```yaml
environment:
  - COMMAND_NAME=-jar yudao-server.jar --spring.profiles.active=prod --server.port=48080 --spring.datasource.url=******************************************
```

## 🔧 部署模式

### 标准单应用部署
**适用场景**: 一个前端对应一个后端的标准部署
```yaml
version: '3.8'
services:
  app-name:
    image: zhao0829wang/java-nginx:1.0
    container_name: app-name
    ports:
      - "8080:80"
    volumes:
      - ./workdir:/workdir
      - ./ui:/home/<USER>/projects/ruoyi-ui
      - ./nginx.conf:/etc/nginx/nginx.conf
    environment:
      - COMMAND_NAME=-jar yudao-server.jar --spring.profiles.active=prod
```

### 多前端共享后端部署
**适用场景**: 多个前端版本共享同一个后端服务（如hyqm_prod和hyqm_qm）

**方案1: 单容器多路径**
```nginx
server {
    listen 80;
    
    # 主前端
    location / {
        root /home/<USER>/projects/ruoyi-ui;
        try_files $uri $uri/ /index.html;
    }
    
    # 第二前端通过路径区分
    location /qm/ {
        alias /home/<USER>/projects/ruoyi-ui-qm/;
        try_files $uri $uri/ /qm/index.html;
    }
    
    # 共享的后端API
    location /prod-api/ {
        proxy_pass http://localhost:48080/;
    }
}
```

**方案2: 多容器共享后端**
- 多个容器运行相同的Java应用
- 每个容器配置不同的前端
- 通过不同端口对外服务

## 🚀 成功案例

### 已部署的应用
1. **hyrm-imppc** (警察学院)
   - 端口: 8080
   - 状态: 运行正常
   - 特点: 标准的单前端单后端部署

2. **hyrm-imusti** (监察官学院)
   - 端口: 8085
   - 状态: 运行正常
   - 特点: 使用特定的imusti profile配置

### 配置特点
- 数据库连接: 使用mysql-prod容器
- Redis连接: 使用redis-prod容器
- RabbitMQ连接: 使用rabbitmq-prod容器
- 网络: 1panel-network

## ⚠️ 注意事项

### 端口配置
- **容器内部**: nginx监听80端口，Java应用监听48080端口
- **对外暴露**: 通过docker端口映射暴露nginx的80端口
- **内部通信**: nginx通过localhost:48080代理到Java应用

### 环境变量
- `COMMAND_NAME`: Java应用启动命令
- `TZ=Asia/Shanghai`: 时区设置

### 数据持久化
- Java应用文件: 挂载到/workdir
- 前端文件: 挂载到/home/<USER>/projects/ruoyi-ui
- 日志文件: 挂载到/home/<USER>/logs

### 网络配置
- 必须使用1panel-network网络
- 容器间通信使用容器名称（如mysql-prod、redis-prod）

## 🔍 故障排查

### 常见问题
1. **Java应用启动失败**: 检查COMMAND_NAME环境变量和JAR文件
2. **前端访问404**: 检查前端文件挂载路径和nginx配置
3. **API请求失败**: 检查nginx代理配置和Java应用端口
4. **数据库连接失败**: 检查容器网络和数据库连接字符串

### 日志查看
```bash
# 查看容器日志
docker logs container-name

# 进入容器检查
docker exec -it container-name /bin/bash

# 检查nginx进程
docker exec container-name ps aux | grep nginx

# 检查Java进程
docker exec container-name ps aux | grep java
```

## 📚 最佳实践

### 配置管理
- 使用外部nginx.conf文件覆盖默认配置
- 通过环境变量传递Java应用参数
- 使用.env文件管理环境变量

### 安全考虑
- 不直接暴露48080端口到外部
- 所有外部访问通过nginx代理
- 使用雷池WAF进行外部防护

### 性能优化
- 启用nginx gzip压缩
- 配置合适的client_max_body_size
- 根据应用需求调整Java内存参数

这个镜像为Java Web应用提供了完整的运行环境，是汇远系列项目迁移的标准化基础镜像。