# 汇远融媒业务运维手册

## 项目概览

**项目群**: 汇远融媒生态系统  
**包含业务**: 汇远融媒(hyrm)、汇远轻媒(hyqm)、校对助手(jiaodui2)  
**技术架构**: Spring Boot + MySQL + Redis + RabbitMQ  
**服务器环境**: Ubuntu 24.04 + Docker容器化部署  
**域名组**: hyrm.nmzyb.cn, hyqm.nmzyb.cn, qm.nmzyb.cn, jiaodui2.nmzyb.cn

## 服务架构

```
外部访问 → 雷池WAF → 1Panel OpenResty → Spring Boot容器
                                      ↓
                    MySQL + Redis + RabbitMQ 容器
```

### 核心容器服务

| 容器名称 | 镜像 | 端口映射 | 用途 |
|---------|------|----------|------|
| java-hyrm-prod | openjdk:11-jdk | 8080:8080 | 汇远融媒主应用 |
| java-hyrm-imusti | openjdk:11-jdk | 8081:8080 | 汇远融媒-imusti模块 |
| java-hyrm-imppc | openjdk:11-jdk | 8082:8080 | 汇远融媒-imppc模块 |
| java-hyqm-prod | openjdk:11-jdk | 8083:8080 | 汇远轻媒主应用 |
| java-hyqm2-prod | openjdk:11-jdk | 8084:8080 | 汇远轻媒2.0版本 |
| java-jiaodui-prod | openjdk:11-jdk | 8085:8080 | 校对助手应用 |
| mysql-prod | mysql:5.7.44 | 3306 | 数据库服务 |
| redis-prod | redis:8.0.3 | 6379 | 缓存服务 |
| rabbitmq-prod | rabbitmq:management | 5672:5672, 15672:15672 | 消息队列服务 |

## 目录结构

```
/mnt/datadisk0/
├── apps/java-web/                # Java应用配置
│   ├── hyrm/
│   │   ├── docker-compose-hyrm.yml       # 融媒主应用
│   │   ├── docker-compose-imusti.yml     # imusti模块
│   │   ├── docker-compose-imppc.yml      # imppc模块
│   │   └── .env                           # 环境变量
│   ├── hyqm/
│   │   ├── docker-compose-hyqm.yml       # 轻媒主应用
│   │   ├── docker-compose-hyqm2.yml      # 轻媒2.0版本
│   │   └── .env                           # 环境变量
│   └── jiaodui/
│       ├── docker-compose.yml            # 校对助手
│       └── .env                           # 环境变量
├── volumns/java-web/             # Java应用数据
│   ├── hyrm_prod/                        # 融媒应用目录
│   ├── hyrm_imusti/                      # imusti模块目录
│   ├── hyrm_imppc/                       # imppc模块目录
│   ├── hyqm_prod/                        # 轻媒应用目录
│   ├── hyqm2_prod/                       # 轻媒2.0目录
│   └── jiaodui_prod/                     # 校对助手目录
└── backups/hyrm/                 # 备份文件
```

## 数据库信息

### 主要数据库

| 数据库名 | 业务 | 大小 | 用途 |
|---------|------|------|------|
| ruoyi_hyrm | 汇远融媒 | ~3.2GB | 融媒体内容管理 |
| ruoyi_imusti | imusti模块 | ~800MB | 内蒙古科技厅业务 |
| ruoyi_imppc | imppc模块 | ~600MB | 内蒙古人大业务 |
| ruoyi_hyqm | 汇远轻媒 | ~1.5GB | 轻量级媒体管理 |
| ruoyi_hyqm2 | 汇远轻媒2.0 | ~900MB | 轻媒升级版本 |
| ruoyi_jiaodui | 校对助手 | ~300MB | 文本校对功能 |

### 数据库连接信息
- **主机**: mysql-prod (容器内网络)
- **用户**: pearproject
- **密码**: kG7#tPq9@zR2$vX1
- **端口**: 3306

## 日常维护操作

### 1. 服务状态检查

```bash
# 检查所有汇远相关容器状态
docker ps | grep -E "(hyrm|hyqm|jiaodui)"

# 检查容器资源使用情况
docker stats --no-stream | grep -E "(hyrm|hyqm|jiaodui)"

# 检查服务端口
netstat -tlnp | grep -E "(8080|8081|8082|8083|8084|8085)"

# 检查JVM内存使用
docker exec java-hyrm-prod jstat -gc $(docker exec java-hyrm-prod jps | grep -v Jps | awk '{print $1}')
```

### 2. 应用日志查看

#### Spring Boot应用日志
```bash
# 汇远融媒主应用日志
docker logs java-hyrm-prod --tail 100 -f

# imusti模块日志
docker logs java-hyrm-imusti --tail 100 -f

# imppc模块日志
docker logs java-hyrm-imppc --tail 100 -f

# 汇远轻媒日志
docker logs java-hyqm-prod --tail 100 -f
docker logs java-hyqm2-prod --tail 100 -f

# 校对助手日志
docker logs java-jiaodui-prod --tail 100 -f

# Spring Boot内部日志文件
docker exec java-hyrm-prod tail -f /app/logs/spring.log
docker exec java-hyqm-prod tail -f /app/logs/spring.log
```

#### 1Panel网站访问日志
```bash
# 汇远融媒访问日志
tail -f /opt/1panel/www/sites/hyrm.nmzyb.cn/log/access.log
tail -f /opt/1panel/www/sites/imusti.hyrm.nmzyb.cn/log/access.log
tail -f /opt/1panel/www/sites/imppc.hyrm.nmzyb.cn/log/access.log

# 汇远轻媒访问日志
tail -f /opt/1panel/www/sites/hyqm.nmzyb.cn/log/access.log
tail -f /opt/1panel/www/sites/qm.nmzyb.cn/log/access.log
tail -f /opt/1panel/www/sites/hyqm2.nmzyb.cn/log/access.log

# 校对助手访问日志
tail -f /opt/1panel/www/sites/jiaodui2.nmzyb.cn/log/access.log

# 统计各业务访问量
grep "$(date '+%d/%b/%Y')" /opt/1panel/www/sites/hyrm.nmzyb.cn/log/access.log | wc -l
grep "$(date '+%d/%b/%Y')" /opt/1panel/www/sites/hyqm.nmzyb.cn/log/access.log | wc -l
```

### 3. 数据库维护

```bash
# 连接到各业务数据库
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyqm
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_jiaodui

# 查看所有汇远相关数据库大小
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' -e "
SELECT 
    table_schema as 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema LIKE 'ruoyi_%' 
GROUP BY table_schema 
ORDER BY SUM(data_length + index_length) DESC;"

# 查看系统用户活跃度
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT DATE(login_date) as date, COUNT(*) as login_count 
FROM sys_logininfor 
WHERE login_date > DATE_SUB(NOW(), INTERVAL 7 DAY) 
GROUP BY DATE(login_date) 
ORDER BY date DESC;"

# 备份所有汇远相关数据库
for db in ruoyi_hyrm ruoyi_imusti ruoyi_imppc ruoyi_hyqm ruoyi_hyqm2 ruoyi_jiaodui; do
    docker exec mysql-prod mysqldump -u root -p'56d9DavJ*zwrwj9rmA' --single-transaction --routines --triggers $db > /tmp/${db}_$(date +%Y%m%d).sql
done
```

### 4. 缓存管理

```bash
# Redis缓存状态
docker exec redis-prod redis-cli INFO memory

# 查看汇远相关缓存键
docker exec redis-prod redis-cli KEYS "*hyrm*"
docker exec redis-prod redis-cli KEYS "*hyqm*"
docker exec redis-prod redis-cli KEYS "*jiaodui*"

# 查看登录会话缓存
docker exec redis-prod redis-cli KEYS "*login_tokens*"
docker exec redis-prod redis-cli KEYS "*captcha*"

# 清理过期缓存
docker exec redis-prod redis-cli KEYS "*expired*" | xargs docker exec redis-prod redis-cli DEL

# 重启缓存服务
docker restart redis-prod
```

### 5. 消息队列管理

```bash
# 检查RabbitMQ状态
docker exec rabbitmq-prod rabbitmqctl status

# 查看队列信息
docker exec rabbitmq-prod rabbitmqctl list_queues

# 查看消息积压情况
docker exec rabbitmq-prod rabbitmqctl list_queues name messages

# 访问RabbitMQ管理界面
echo "RabbitMQ管理界面: http://服务器IP:15672"
echo "默认用户名/密码: guest/guest"

# 清理积压消息（谨慎操作）
docker exec rabbitmq-prod rabbitmqctl purge_queue queue_name
```

### 6. 代码更新部署

```bash
# 进入应用目录
cd /mnt/datadisk0/volumns/java-web

# 更新各业务代码
cd hyrm_prod && git pull origin master
cd ../hyrm_imusti && git pull origin master
cd ../hyrm_imppc && git pull origin master
cd ../hyqm_prod && git pull origin master
cd ../jiaodui_prod && git pull origin master

# 重新构建JAR包（如果需要）
cd hyrm_prod && mvn clean package -DskipTests

# 重启相关容器
docker restart java-hyrm-prod java-hyrm-imusti java-hyrm-imppc
docker restart java-hyqm-prod java-hyqm2-prod
docker restart java-jiaodui-prod
```

## 业务特定操作

### 融媒体内容管理 (hyrm.nmzyb.cn)

```bash
# 检查内容发布状态
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT status, COUNT(*) as count 
FROM cms_article 
WHERE created_time > CURDATE() 
GROUP BY status;"

# 查看最新发布的文章
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT id, title, author, status, created_time 
FROM cms_article 
ORDER BY created_time DESC 
LIMIT 10;"

# 检查媒体文件上传情况
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT DATE(upload_time) as date, COUNT(*) as upload_count, 
       SUM(file_size)/1024/1024 as 'Total_MB'
FROM sys_file_info 
WHERE upload_time > DATE_SUB(NOW(), INTERVAL 7 DAY) 
GROUP BY DATE(upload_time) 
ORDER BY date DESC;"
```

### 轻媒管理系统 (hyqm.nmzyb.cn)

```bash
# 检查轻媒内容统计
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyqm -e "
SELECT content_type, COUNT(*) as count 
FROM content_library 
WHERE created_time > DATE_SUB(NOW(), INTERVAL 1 DAY) 
GROUP BY content_type;"

# 查看用户活跃度
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyqm -e "
SELECT COUNT(DISTINCT user_id) as active_users 
FROM user_operation_log 
WHERE operation_time > DATE_SUB(NOW(), INTERVAL 1 DAY);"
```

### 校对助手 (jiaodui2.nmzyb.cn)

```bash
# 检查校对任务状态
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_jiaodui -e "
SELECT task_status, COUNT(*) as count 
FROM proofreading_task 
WHERE created_time > CURDATE() 
GROUP BY task_status;"

# 查看最近校对记录
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_jiaodui -e "
SELECT id, document_name, error_count, status, created_time 
FROM proofreading_record 
ORDER BY created_time DESC 
LIMIT 10;"
```

## 故障排查

### 常见问题

1. **Spring Boot应用启动失败**
   ```bash
   # 检查JVM内存设置
   docker exec java-hyrm-prod java -XX:+PrintFlagsFinal -version | grep HeapSize
   
   # 查看详细启动日志
   docker logs java-hyrm-prod --tail 200
   
   # 检查配置文件
   docker exec java-hyrm-prod cat /app/application.yml
   ```

2. **数据库连接超时**
   ```bash
   # 检查数据库连接池状态
   docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' -e "SHOW PROCESSLIST;"
   
   # 重启应用容器
   docker restart java-hyrm-prod
   ```

3. **文件上传失败**
   ```bash
   # 检查上传目录权限
   docker exec java-hyrm-prod ls -la /app/upload/
   
   # 检查磁盘空间
   df -h /mnt/datadisk0
   
   # 清理临时文件
   docker exec java-hyrm-prod find /app/temp -mtime +1 -delete
   ```

4. **消息队列异常**
   ```bash
   # 重启RabbitMQ
   docker restart rabbitmq-prod
   
   # 检查队列连接
   docker exec rabbitmq-prod rabbitmqctl list_connections
   ```

## 性能监控

### JVM监控

```bash
# 查看JVM内存使用
docker exec java-hyrm-prod jstat -gc $(docker exec java-hyrm-prod jps | grep -v Jps | awk '{print $1}') 5s

# 查看线程数
docker exec java-hyrm-prod jstack $(docker exec java-hyrm-prod jps | grep -v Jps | awk '{print $1}') | grep "java.lang.Thread.State" | wc -l

# CPU使用监控
docker exec java-hyrm-prod top -p $(docker exec java-hyrm-prod jps | grep -v Jps | awk '{print $1}')
```

### 应用性能监控

```bash
# 接口响应时间监控
curl -w "@curl-format.txt" -o /dev/null -s "http://hyrm.nmzyb.cn/health"

# 数据库慢查询监控
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' -e "
SELECT query_time, lock_time, rows_sent, rows_examined, sql_text 
FROM mysql.slow_log 
WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 HOUR) 
ORDER BY query_time DESC LIMIT 5;"
```

## 备份策略

### 数据备份
- **数据库**: 每日凌晨4点自动备份
- **上传文件**: 每日增量备份
- **代码**: Git仓库备份
- **配置文件**: 每日备份

### 备份脚本
```bash
#!/bin/bash
# 汇远融媒业务备份脚本
DATE=$(date +%Y%m%d)
BACKUP_DIR="/mnt/datadisk0/backups/hyrm/$DATE"
mkdir -p $BACKUP_DIR

# 数据库备份
for db in ruoyi_hyrm ruoyi_imusti ruoyi_imppc ruoyi_hyqm ruoyi_hyqm2 ruoyi_jiaodui; do
    docker exec mysql-prod mysqldump -u root -p'56d9DavJ*zwrwj9rmA' --single-transaction --routines --triggers $db > $BACKUP_DIR/${db}.sql
done

# 配置备份
cp -r /mnt/datadisk0/apps/java-web $BACKUP_DIR/

# 上传文件备份（仅备份最近修改的文件）
find /mnt/datadisk0/volumns/java-web -name "upload" -type d -exec rsync -av --include="*/" --include="*.jpg" --include="*.png" --include="*.pdf" --exclude="*" {} $BACKUP_DIR/uploads/ \;

echo "汇远融媒备份完成: $BACKUP_DIR"
```

## 安全配置

### 访问控制
```bash
# 检查防火墙规则
ufw status

# 检查登录失败记录
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT ipaddr, COUNT(*) as failed_attempts 
FROM sys_logininfor 
WHERE status = '1' AND info_time > DATE_SUB(NOW(), INTERVAL 1 DAY) 
GROUP BY ipaddr 
HAVING failed_attempts > 5;"
```

### 数据安全
```bash
# 检查敏感数据加密状态
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_hyrm -e "
SELECT COUNT(*) as encrypted_users 
FROM sys_user 
WHERE password LIKE '\$2a\$%';"
```

## 更新记录

| 日期 | 版本 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 2025-07-29 | v1.0 | 初始版本创建 | Claude |

---
**维护负责人**: 运维团队  
**紧急联系**: 技术支持部门  
**业务联系人**: 汇远融媒产品团队