# 芋道架构系统运维手册 - 学院业务

## 项目概览

**系统名称**: 内大监察官学院 + 内蒙古警察学院业务系统  
**技术框架**: 芋道框架 (YuDao)  
**访问域名**: 
- imusti.hyrm.nmzyb.cn (内大监察官学院)
- imppc.hyrm.nmzyb.cn (内蒙古警察学院)  
**技术架构**: Spring Boot + MySQL + Redis  
**服务器环境**: Ubuntu 24.04 + Docker容器化部署  
**数据库**: 
- hyrm_imusti (~800MB) - 内大监察官学院业务
- hyrm_imppc (~600MB) - 内蒙古警察学院业务

## 服务架构

```
外部访问 → 雷池WAF → 1Panel OpenResty → 学院业务容器组
                                      ↓
                    MySQL + Redis 容器
```

### 核心容器服务

| 容器名称 | 镜像 | 端口映射 | 用途 |
|---------|------|----------|------|
| hyrm-imusti | zhao0829wang/java-nginx:1.0 | 8085:80 | 内大监察官学院系统 |
| hyrm-imppc | zhao0829wang/java-nginx:1.0 | 8080:80 | 内蒙古警察学院系统 |
| mysql-prod | mysql:5.7.44 | 3306 | 数据库服务 |
| redis-prod | redis:8.0.3 | 6379 | 缓存服务 |

## 目录结构

```
/mnt/datadisk0/
├── apps/java-web/
│   ├── hyrm-imusti/
│   │   └── docker-compose.yml           # 内大监察官学院容器配置
│   └── hyrm-imppc/
│       └── docker-compose.yml           # 内蒙古警察学院容器配置
├── volumns/java-web/
│   ├── hyrm-imusti/                     # 内大监察官学院应用数据
│   │   ├── workdir/                     # 应用工作目录
│   │   ├── ui/                          # 前端静态文件(ruoyi-ui)
│   │   ├── logs/                        # 应用日志(/home/<USER>/logs)
│   │   └── nginx.conf                   # Nginx配置文件
│   └── hyrm-imppc/                      # 内蒙古警察学院应用数据
│       ├── workdir/                     # 应用工作目录
│       ├── ui/                          # 前端静态文件(ruoyi-ui)
│       ├── logs/                        # 应用日志(/home/<USER>/logs)
│       └── nginx.conf                   # Nginx配置文件
└── backups/hyrm/                        # 备份文件
```

## 数据库信息

### 主要数据库

| 数据库名 | 业务系统 | 大小 | 用途 |
|---------|---------|------|------|
| hyrm_imusti | 内大监察官学院 | ~800MB | 内大监察官学院业务管理 |
| hyrm_imppc | 内蒙古警察学院 | ~600MB | 内蒙古警察学院业务管理 |
| hyrm_news | 共享新闻数据 | 共享 | 两个学院系统共享的新闻数据源 |

### 数据库连接信息
- **主机**: mysql-prod (容器内网络)
- **用户**: pearproject
- **密码**: kG7#tPq9@zR2$vX1
- **端口**: 3306

### 芋道框架核心数据表
```sql
-- 系统管理模块
system_users          -- 用户表
system_roles          -- 角色表
system_menus          -- 菜单权限表
system_dept           -- 部门表
system_dict_type      -- 字典类型表
system_dict_data      -- 字典数据表

-- 基础设施模块
infra_file            -- 文件存储表
infra_config          -- 参数配置表
infra_job             -- 定时任务表
infra_api_access_log  -- API访问日志表
infra_api_error_log   -- API错误日志表

-- 业务特定表（根据各学院业务而定）
-- imusti业务表
imusti_course         -- 课程管理
imusti_student        -- 学员管理
imusti_teacher        -- 教师管理

-- imppc业务表  
imppc_case            -- 案例管理
imppc_training        -- 培训管理
imppc_exam            -- 考试管理
```

## 日常维护操作

### 1. 服务状态检查

```bash
# 检查学院业务容器状态
docker ps | grep -E "(hyrm-imusti|hyrm-imppc)"

# 检查容器资源使用情况
docker stats --no-stream hyrm-imusti hyrm-imppc

# 检查应用端口
netstat -tlnp | grep -E "(8085|8080)"

# 检查JVM内存使用
docker exec hyrm-imusti jstat -gc $(docker exec hyrm-imusti jps | grep -v Jps | awk '{print $1}')
docker exec hyrm-imppc jstat -gc $(docker exec hyrm-imppc jps | grep -v Jps | awk '{print $1}')
```

### 2. 应用日志查看

#### Spring Boot应用日志
```bash
# 内大监察官学院日志
docker logs hyrm-imusti --tail 100 -f

# 内蒙古警察学院日志
docker logs hyrm-imppc --tail 100 -f

# Spring Boot内部日志文件
docker exec hyrm-imusti tail -f /home/<USER>/logs/spring.log
docker exec hyrm-imppc tail -f /home/<USER>/logs/spring.log

# 业务日志
docker exec hyrm-imusti tail -f /home/<USER>/logs/biz.log
docker exec hyrm-imppc tail -f /home/<USER>/logs/biz.log

# 错误日志
docker exec hyrm-imusti tail -f /home/<USER>/logs/error.log
docker exec hyrm-imppc tail -f /home/<USER>/logs/error.log
```

#### 1Panel网站访问日志
```bash
# 内大监察官学院访问日志
tail -f /opt/1panel/www/sites/imusti.hyrm.nmzyb.cn/log/access.log

# 内蒙古警察学院访问日志
tail -f /opt/1panel/www/sites/imppc.hyrm.nmzyb.cn/log/access.log

# 统计今日访问量
grep "$(date '+%d/%b/%Y')" /opt/1panel/www/sites/imusti.hyrm.nmzyb.cn/log/access.log | wc -l
grep "$(date '+%d/%b/%Y')" /opt/1panel/www/sites/imppc.hyrm.nmzyb.cn/log/access.log | wc -l

# 分析访问IP统计
awk '{print $1}' /opt/1panel/www/sites/imusti.hyrm.nmzyb.cn/log/access.log | sort | uniq -c | sort -nr | head -10
awk '{print $1}' /opt/1panel/www/sites/imppc.hyrm.nmzyb.cn/log/access.log | sort | uniq -c | sort -nr | head -10
```

### 3. 数据库维护

```bash
# 连接各学院数据库
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imusti
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imppc

# 查看数据库大小
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' -e "
SELECT 
    table_schema as 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema IN ('ruoyi_imusti', 'ruoyi_imppc')
GROUP BY table_schema;"

# 查看各学院表占用空间排序
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imusti -e "
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as 'Size (MB)'
FROM information_schema.TABLES 
WHERE table_schema = 'ruoyi_imusti'
ORDER BY (data_length + index_length) DESC LIMIT 10;"

docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imppc -e "
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as 'Size (MB)'
FROM information_schema.TABLES 
WHERE table_schema = 'ruoyi_imppc'
ORDER BY (data_length + index_length) DESC LIMIT 10;"
```

### 4. 缓存管理

```bash
# Redis缓存状态
docker exec redis-prod redis-cli INFO memory

# 查看学院相关缓存键
docker exec redis-prod redis-cli KEYS "*imusti*"
docker exec redis-prod redis-cli KEYS "*imppc*"

# 查看用户会话缓存
docker exec redis-prod redis-cli KEYS "*login_tokens*"
docker exec redis-prod redis-cli KEYS "*captcha*"

# 查看系统配置缓存（芋道框架特有）
docker exec redis-prod redis-cli KEYS "*system:config*"
docker exec redis-prod redis-cli KEYS "*system:dict*"
docker exec redis-prod redis-cli KEYS "*system:menu*"

# 清理特定学院缓存
docker exec redis-prod redis-cli DEL $(docker exec redis-prod redis-cli KEYS "*imusti*")
docker exec redis-prod redis-cli DEL $(docker exec redis-prod redis-cli KEYS "*imppc*")
```

## 业务特定操作

### 内大监察官学院 (imusti)

```bash
# 检查课程管理状态
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imusti -e "
SELECT status, COUNT(*) as count 
FROM imusti_course 
WHERE create_time > CURDATE() 
GROUP BY status;"

# 查看学员注册情况
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imusti -e "
SELECT DATE(create_time) as date, COUNT(*) as student_count
FROM imusti_student 
WHERE create_time > DATE_SUB(NOW(), INTERVAL 7 DAY) 
GROUP BY DATE(create_time) 
ORDER BY date DESC;"

# 查看教师活跃度
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imusti -e "
SELECT teacher_name, last_login_time, status
FROM imusti_teacher 
WHERE last_login_time > DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY last_login_time DESC;"
```

### 内蒙古警察学院 (imppc)

```bash
# 检查案例管理状态
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imppc -e "
SELECT case_status, COUNT(*) as count 
FROM imppc_case 
WHERE create_time > CURDATE() 
GROUP BY case_status;"

# 查看培训进度
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imppc -e "
SELECT training_name, progress_status, COUNT(*) as participant_count
FROM imppc_training 
WHERE start_time > DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY training_name, progress_status;"

# 查看考试统计
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imppc -e "
SELECT exam_name, AVG(score) as avg_score, COUNT(*) as participant_count
FROM imppc_exam 
WHERE exam_time > DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY exam_name 
ORDER BY exam_time DESC;"
```

### 用户管理监控

```bash
# 查看内大监察官学院用户活跃度
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imusti -e "
SELECT DATE(login_date) as date, COUNT(*) as login_count 
FROM system_login_log 
WHERE login_date > DATE_SUB(NOW(), INTERVAL 7 DAY) 
GROUP BY DATE(login_date) 
ORDER BY date DESC;"

# 查看内蒙古警察学院用户活跃度
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imppc -e "
SELECT DATE(login_date) as date, COUNT(*) as login_count 
FROM system_login_log 
WHERE login_date > DATE_SUB(NOW(), INTERVAL 7 DAY) 
GROUP BY DATE(login_date) 
ORDER BY date DESC;"

# 检查用户权限分布
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imusti -e "
SELECT r.name as role_name, COUNT(ur.user_id) as user_count
FROM system_roles r 
LEFT JOIN system_user_roles ur ON r.id = ur.role_id 
GROUP BY r.id, r.name 
ORDER BY user_count DESC;"
```

## 代码更新部署

```bash
# 进入内大监察官学院应用目录
cd /mnt/datadisk0/volumns/java-web/hyrm_imusti

# 更新代码
git pull origin master

# 重新构建JAR包
mvn clean package -DskipTests

# 备份当前版本
cp target/yudao-server.jar target/yudao-server.jar.backup.$(date +%Y%m%d)

# 重启内大监察官学院容器
docker restart java-hyrm-imusti

# 检查启动状态
docker logs java-hyrm-imusti --tail 50

# 验证服务可用性
curl -I http://imusti.hyrm.nmzyb.cn/

# 对内蒙古警察学院执行相同操作
cd /mnt/datadisk0/volumns/java-web/hyrm_imppc
git pull origin master
mvn clean package -DskipTests
cp target/yudao-server.jar target/yudao-server.jar.backup.$(date +%Y%m%d)
docker restart java-hyrm-imppc
docker logs java-hyrm-imppc --tail 50
curl -I http://imppc.hyrm.nmzyb.cn/
```

## 故障排查

### 常见问题

1. **Spring Boot应用启动失败**
   ```bash
   # 检查内大监察官学院JVM内存设置
   docker exec java-hyrm-imusti java -XX:+PrintFlagsFinal -version | grep HeapSize
   
   # 检查内蒙古警察学院JVM内存设置
   docker exec java-hyrm-imppc java -XX:+PrintFlagsFinal -version | grep HeapSize
   
   # 查看详细启动日志
   docker logs java-hyrm-imusti --tail 200
   docker logs java-hyrm-imppc --tail 200
   
   # 检查配置文件
   docker exec java-hyrm-imusti cat /app/application.yml
   docker exec java-hyrm-imppc cat /app/application.yml
   ```

2. **数据库连接超时**
   ```bash
   # 检查数据库连接池状态
   docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' -e "SHOW PROCESSLIST;"
   
   # 检查各学院应用的数据库连接
   docker exec java-hyrm-imusti netstat -an | grep 3306
   docker exec java-hyrm-imppc netstat -an | grep 3306
   
   # 重启应用容器
   docker restart java-hyrm-imusti java-hyrm-imppc
   ```

3. **文件上传失败**
   ```bash
   # 检查上传目录权限
   docker exec java-hyrm-imusti ls -la /app/upload/
   docker exec java-hyrm-imppc ls -la /app/upload/
   
   # 检查磁盘空间
   df -h /mnt/datadisk0
   
   # 清理临时文件
   docker exec java-hyrm-imusti find /app/temp -mtime +1 -delete
   docker exec java-hyrm-imppc find /app/temp -mtime +1 -delete
   ```

4. **芋道框架特定问题**
   ```bash
   # 检查系统配置状态
   docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imusti -e "
   SELECT config_key, config_value, config_type, remark 
   FROM infra_config 
   WHERE deleted = 0 
   ORDER BY create_time DESC LIMIT 10;"
   
   # 检查定时任务状态
   docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imusti -e "
   SELECT name, cron_expression, status, handler_name 
   FROM infra_job 
   WHERE deleted = 0;"
   
   # 检查API访问日志
   docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imusti -e "
   SELECT request_url, user_id, user_type, result_code, duration, create_time
   FROM infra_api_access_log 
   WHERE create_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)
   AND result_code != 0
   ORDER BY create_time DESC LIMIT 10;"
   ```

## 安全配置

### 访问控制
```bash
# 检查防火墙规则
ufw status

# 检查内大监察官学院登录失败记录
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imusti -e "
SELECT user_ip, COUNT(*) as failed_attempts, MAX(create_time) as last_attempt
FROM system_login_log 
WHERE result = 1 AND create_time > DATE_SUB(NOW(), INTERVAL 1 DAY) 
GROUP BY user_ip 
HAVING failed_attempts > 5
ORDER BY failed_attempts DESC;"

# 检查内蒙古警察学院登录失败记录
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imppc -e "
SELECT user_ip, COUNT(*) as failed_attempts, MAX(create_time) as last_attempt
FROM system_login_log 
WHERE result = 1 AND create_time > DATE_SUB(NOW(), INTERVAL 1 DAY) 
GROUP BY user_ip 
HAVING failed_attempts > 5
ORDER BY failed_attempts DESC;"
```

### 数据安全
```bash
# 检查内大监察官学院敏感数据加密状态
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imusti -e "
SELECT COUNT(*) as encrypted_users 
FROM system_users 
WHERE password LIKE '\$2a\$%';"

# 检查内蒙古警察学院敏感数据加密状态
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imppc -e "
SELECT COUNT(*) as encrypted_users 
FROM system_users 
WHERE password LIKE '\$2a\$%';"

# 检查权限配置
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imusti -e "
SELECT u.username, r.name as role_name, r.code as role_code, u.status
FROM system_users u 
JOIN system_user_roles ur ON u.id = ur.user_id 
JOIN system_roles r ON ur.role_id = r.id 
WHERE u.status = 0 AND r.status = 0
ORDER BY u.username;"
```

## 芋道框架特色功能维护

### 基础设施管理
```bash
# 查看文件存储状态
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imusti -e "
SELECT config_id, name, storage, path, url, create_time
FROM infra_file 
ORDER BY create_time DESC LIMIT 10;"

# 查看参数配置
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imusti -e "
SELECT config_key, config_value, config_type, remark
FROM infra_config 
WHERE deleted = 0 
ORDER BY create_time DESC;"

# 清理过期文件
docker exec java-hyrm-imusti find /app/upload -name "*.tmp" -mtime +7 -delete
docker exec java-hyrm-imppc find /app/upload -name "*.tmp" -mtime +7 -delete
```

### 定时任务管理
```bash
# 查看定时任务执行状态
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imusti -e "
SELECT name, handler_name, cron_expression, status, 
       retry_count, next_time, create_time
FROM infra_job 
WHERE deleted = 0 
ORDER BY create_time DESC;"

# 查看定时任务日志
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imusti -e "
SELECT job_id, handler_name, status, result, duration, create_time
FROM infra_job_log 
WHERE create_time > DATE_SUB(NOW(), INTERVAL 1 DAY)
ORDER BY create_time DESC LIMIT 20;"
```

### 系统监控
```bash
# 查看API错误统计
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imusti -e "
SELECT process_status, exception_name, COUNT(*) as error_count
FROM infra_api_error_log 
WHERE process_time > DATE_SUB(NOW(), INTERVAL 1 DAY)
GROUP BY process_status, exception_name 
ORDER BY error_count DESC;"

# 查看用户操作统计
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' ruoyi_imusti -e "
SELECT module, type, COUNT(*) as operation_count
FROM system_operate_log 
WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 DAY)
GROUP BY module, type 
ORDER BY operation_count DESC;"
```

## 更新记录

| 日期 | 版本 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 2025-07-30 | v1.0 | 基于芋道框架的学院业务系统独立运维手册 | Claude |

---
**维护负责人**: 运维团队  
**紧急联系**: 技术支持部门  
**业务联系人**: 
- 内大监察官学院业务团队
- 内蒙古警察学院业务团队