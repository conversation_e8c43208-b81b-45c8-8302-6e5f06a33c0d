# Claude Code 记忆机制与权限管理

## 记忆机制

### 三层记忆体系

Claude Code提供三种类型的记忆位置：

**1. 项目记忆 (`./CLAUDE.md`)**
- 位置：项目根目录
- 用途：团队共享的项目架构和工作流程指令
- 特点：所有团队成员共同维护和使用

**2. 用户记忆 (`~/.claude/CLAUDE.md`)**
- 位置：用户主目录 `C:\Users\<USER>\.claude\CLAUDE.md`
- 用途：个人偏好设置，跨所有项目生效
- 特点：个人定制化配置

**3. 项目记忆（本地）- 已弃用**
- 原用途：项目特定的个人偏好
- 状态：不再推荐使用

### 记忆管理特性

**自动加载机制**
- Claude Code启动时自动加载所有记忆文件
- 递归读取目录树中的记忆文件

**文件导入语法**
- 使用 `@path/to/import` 语法导入其他记忆文件
- 支持记忆文件的模块化管理

**快速添加**
- 输入以 `#` 开头可快速添加记忆
- 使用 `/memory` 命令直接编辑记忆文件

**最佳实践**
- 指令要具体明确
- 使用结构化Markdown格式
- 定期审查和更新记忆内容

## 配置系统 (settings.json)

### 配置文件位置

**用户级配置**
```
~/.claude/settings.json
```

**项目级配置**
```
.claude/settings.json          # 团队共享配置
.claude/settings.local.json    # 个人项目配置
```

**企业级配置**
- 系统特定路径的企业管理策略文件

### 主要配置项

**权限控制**
```json
{
  "permissions": {
    "allow": ["Bash(git diff:*)"],
    "deny": ["WebFetch", "Bash(curl:*)"]
  }
}
```

**环境变量**
```json
{
  "env": {
    "CUSTOM_VAR": "value"
  }
}
```

**钩子脚本**
```json
{
  "hooks": {
    "beforeToolCall": "echo 'Before tool execution'"
  }
}
```

**模型覆盖**
```json
{
  "model": "claude-3-sonnet-20240229"
}
```

**认证助手**
```json
{
  "apiKeyHelper": "/path/to/auth/script"
}
```

### 配置优先级

配置按以下顺序应用（优先级从高到低）：
1. 企业策略
2. 命令行参数
3. 本地项目设置
4. 共享项目设置
5. 用户设置

## 权限机制

### 权限控制工具

需要显式权限的工具：
- `Bash` - 命令行执行
- `Edit` - 文件编辑
- `MultiEdit` - 批量文件编辑
- `NotebookEdit` - Jupyter笔记本编辑
- `WebFetch` - 网页获取
- `WebSearch` - 网络搜索
- `Write` - 文件写入

### 权限配置语法

**允许特定操作**
```json
{
  "permissions": {
    "allow": [
      "Bash(git diff:*)",      // 允许所有git diff命令
      "Edit",                  // 允许所有文件编辑
      "WebFetch(anthropic.com:*)" // 允许访问anthropic.com域名
    ]
  }
}
```

**拒绝特定操作**
```json
{
  "permissions": {
    "deny": [
      "WebFetch",              // 禁用所有网页获取
      "Bash(curl:*)",          // 禁用所有curl命令
      "Write(/etc/*)"          // 禁用系统目录写入
    ]
  }
}
```

### 高级权限设置

**附加工作目录**
```json
{
  "additionalDirectories": ["/path/to/additional/workdir"]
}
```

**默认权限模式**
```json
{
  "defaultMode": "restrictive"
}
```

**禁用权限绕过**
```json
{
  "disableBypassPermissionsMode": true
}
```

## 实际应用建议

### 记忆文件管理
1. **项目CLAUDE.md**：存放架构信息、开发规范、常用命令
2. **用户CLAUDE.md**：存放个人工作习惯、偏好设置
3. **定期维护**：及时更新过时信息，保持内容准确性

### 权限配置策略
1. **最小权限原则**：只开放必需的工具权限
2. **分层管理**：企业策略 → 项目策略 → 个人偏好
3. **安全审计**：定期检查权限配置的合理性

### 团队协作
1. **统一配置**：团队共享的项目配置文件
2. **个人定制**：使用本地配置满足个人需求
3. **文档同步**：将配置变更记录在项目文档中

通过合理配置记忆机制和权限管理，可以大大提升Claude Code的使用效率和安全性。