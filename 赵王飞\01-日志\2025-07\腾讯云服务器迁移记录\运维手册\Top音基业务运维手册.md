# Top音基(yinji)业务运维手册

## 项目概览

**项目名称**: Top音基(yinji)业务系统  
**技术架构**: PHP 7.2 + Apache + MySQL + Redis + 微信生态  
**服务器环境**: Ubuntu 24.04 + Docker容器化部署  
**域名**: 5iyinji.nmzyb.cn

## 服务架构

```
外部访问 → 雷池WAF → 1Panel OpenResty → PHP容器
                                      ↓
                        MySQL + Redis 容器
```

### 核心容器服务

| 容器名称 | 镜像 | 端口映射 | 用途 |
|---------|------|----------|------|
| php-web-yinji | zhao0829wang/apache2-php:7.2-rc4 | 8090:80 | yinji主应用 |
| php-web-yinji-admin | zhao0829wang/apache2-php:7.2-rc4 | 8091:80 | yinji管理后台 |
| mysql-prod | mysql:5.7.44 | 3306 | 数据库服务 |
| redis-prod | redis:8.0.3 | 6379 | 缓存服务 |

## 目录结构

```
/mnt/datadisk0/
├── apps/php-web/yinji/           # yinji应用配置
│   ├── docker-compose-yinji.yml      # yinji主应用容器配置
│   ├── docker-compose-yinji-admin.yml # yinji管理后台容器配置
│   └── .env                           # 环境变量
├── volumns/php-web/yinji/        # yinji应用数据
│   ├── yinji/                         # yinji主应用代码 (4.7GB)
│   │   ├── public/static/             # 媒体文件 (2.2GB)
│   │   ├── .git/                      # Git仓库 (2.2GB)
│   │   └── [应用代码文件]
│   └── yinji-admin/                   # yinji管理后台代码 (15GB)
│       ├── public/wechat/m4a/         # 音频文件 (9.7GB)
│       └── [管理后台文件]
└── backups/yinji/                # 备份文件
```

## 数据库信息

### 主要数据库

| 数据库名 | 用途 | 大小 | 备注 |
|---------|------|------|------|
| yinji | yinji主应用数据 | ~2.5GB | 核心业务数据 |
| yinji_admin | 管理后台数据 | ~800MB | 后台管理数据 |

### 数据库连接信息
- **主机**: mysql-prod (容器内网络)
- **用户**: pearproject
- **密码**: kG7#tPq9@zR2$vX1
- **端口**: 3306

## 日常维护操作

### 1. 服务状态检查

```bash
# 检查yinji容器状态
docker ps | grep yinji

# 检查容器资源使用
docker stats --no-stream | grep yinji

# 检查服务端口
netstat -tlnp | grep -E "(8090|8091)"

# 检查磁盘使用（重要：yinji有大量媒体文件）
du -sh /mnt/datadisk0/volumns/php-web/yinji/*
```

### 2. 应用日志查看

#### 容器应用日志
```bash
# yinji主应用日志
docker logs php-web-yinji --tail 100 -f
docker exec php-web-yinji tail -f /var/log/apache2/error.log

# yinji管理后台日志
docker logs php-web-yinji-admin --tail 100 -f
docker exec php-web-yinji-admin tail -f /var/log/apache2/error.log

# PHP应用日志（ThinkPHP框架）
docker exec php-web-yinji tail -f /var/www/html/runtime/log/$(date +%Y%m)/$(date +%d).log
docker exec php-web-yinji-admin tail -f /var/www/html/runtime/log/$(date +%Y%m)/$(date +%d).log
```

#### 1Panel网站访问日志
```bash
# 查看网站访问日志 (实时)
tail -f /opt/1panel/www/sites/5iyinji.nmzyb.cn/log/access.log

# 查看网站错误日志 (实时)
tail -f /opt/1panel/www/sites/5iyinji.nmzyb.cn/log/error.log

# 查看最近访问记录
tail -100 /opt/1panel/www/sites/5iyinji.nmzyb.cn/log/access.log

# 统计今日访问量
grep "$(date '+%d/%b/%Y')" /opt/1panel/www/sites/5iyinji.nmzyb.cn/log/access.log | wc -l

# 查看访问最多的IP
awk '{print $1}' /opt/1panel/www/sites/5iyinji.nmzyb.cn/log/access.log | sort | uniq -c | sort -nr | head -10

# 查看音频文件访问情况
grep "\.m4a" /opt/1panel/www/sites/5iyinji.nmzyb.cn/log/access.log | tail -20
```

### 3. 数据库维护

```bash
# 连接到yinji数据库
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' yinji
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' yinji_admin

# 查看数据库大小
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' -e "
SELECT 
    table_schema as 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema IN ('yinji', 'yinji_admin')
GROUP BY table_schema;"

# 查看最活跃的表
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' yinji -e "
SELECT table_name, table_rows, 
       ROUND((data_length + index_length) / 1024 / 1024, 2) as 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'yinji' 
ORDER BY (data_length + index_length) DESC LIMIT 10;"

# 备份yinji数据库
docker exec mysql-prod mysqldump -u root -p'56d9DavJ*zwrwj9rmA' --single-transaction --routines --triggers yinji > /tmp/yinji_$(date +%Y%m%d).sql
docker exec mysql-prod mysqldump -u root -p'56d9DavJ*zwrwj9rmA' --single-transaction --routines --triggers yinji_admin > /tmp/yinji_admin_$(date +%Y%m%d).sql
```

### 4. 缓存管理

```bash
# Redis缓存状态
docker exec redis-prod redis-cli INFO memory

# 查看yinji相关缓存键
docker exec redis-prod redis-cli KEYS "*yinji*"

# 清理yinji缓存
docker exec redis-prod redis-cli DEL $(docker exec redis-prod redis-cli KEYS "*yinji*")

# 重启缓存服务
docker restart redis-prod
```

### 5. 媒体文件管理

```bash
# 检查音频文件存储状态
ls -lh /mnt/datadisk0/volumns/php-web/yinji/yinji-admin/public/wechat/m4a/ | head -20

# 统计音频文件数量和大小
find /mnt/datadisk0/volumns/php-web/yinji/yinji-admin/public/wechat/m4a/ -name "*.m4a" | wc -l
du -sh /mnt/datadisk0/volumns/php-web/yinji/yinji-admin/public/wechat/m4a/

# 检查最近上传的音频文件
find /mnt/datadisk0/volumns/php-web/yinji/yinji-admin/public/wechat/m4a/ -name "*.m4a" -mtime -1 -ls

# 清理超过30天的临时文件（谨慎操作）
find /mnt/datadisk0/volumns/php-web/yinji/yinji/public/temp/ -mtime +30 -type f -delete

# 检查静态资源
ls -lh /mnt/datadisk0/volumns/php-web/yinji/yinji/public/static/ | head -10
```

### 6. 代码更新部署

```bash
# 进入代码目录
cd /mnt/datadisk0/volumns/php-web/yinji

# 更新yinji主应用代码
cd yinji && git pull origin master

# 更新yinji管理后台代码  
cd ../yinji-admin && git pull origin master

# 清理应用缓存
docker exec php-web-yinji rm -rf /var/www/html/runtime/cache/*
docker exec php-web-yinji-admin rm -rf /var/www/html/runtime/cache/*

# 重启容器使配置生效
docker restart php-web-yinji php-web-yinji-admin
```

## 业务特定操作

### 微信相关功能

```bash
# 检查微信接口调用情况
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' yinji -e "
SELECT DATE(created_at) as date, COUNT(*) as calls 
FROM wechat_api_log 
WHERE created_at > DATE_SUB(NOW(), INTERVAL 7 DAY) 
GROUP BY DATE(created_at) 
ORDER BY date DESC;"

# 查看微信用户活跃度
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' yinji -e "
SELECT COUNT(DISTINCT openid) as active_users 
FROM user_activity 
WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 DAY);"
```

### 音频处理功能

```bash
# 检查音频转换任务状态
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' yinji_admin -e "
SELECT status, COUNT(*) as count 
FROM audio_conversion_tasks 
WHERE created_at > CURDATE() 
GROUP BY status;"

# 查看最近的音频上传
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' yinji_admin -e "
SELECT id, filename, file_size, status, created_at 
FROM audio_files 
ORDER BY created_at DESC 
LIMIT 10;"
```

### 内容审核功能

```bash
# 检查待审核内容
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' yinji -e "
SELECT content_type, COUNT(*) as pending_count 
FROM content_review 
WHERE status = 'pending' 
GROUP BY content_type;"
```

## 故障排查

### 常见问题

1. **音频文件无法访问**
   ```bash
   # 检查文件权限
   ls -la /mnt/datadisk0/volumns/php-web/yinji/yinji-admin/public/wechat/m4a/
   
   # 修复文件权限
   chown -R www-data:www-data /mnt/datadisk0/volumns/php-web/yinji/yinji-admin/public/wechat/m4a/
   ```

2. **磁盘空间不足**
   ```bash
   # 检查磁盘使用
   df -h /mnt/datadisk0
   
   # 清理日志文件
   docker exec php-web-yinji find /var/log -name "*.log" -mtime +7 -delete
   docker exec php-web-yinji-admin find /var/log -name "*.log" -mtime +7 -delete
   ```

3. **微信接口异常**
   ```bash
   # 检查微信access_token
   docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' yinji -e "
   SELECT * FROM wechat_config WHERE config_key = 'access_token' ORDER BY updated_at DESC LIMIT 1;"
   
   # 重新获取access_token
   docker exec php-web-yinji curl -X POST "http://localhost/api/wechat/refresh_token"
   ```

4. **性能问题**
   ```bash
   # 检查慢查询
   docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' -e "
   SELECT query_time, lock_time, rows_sent, rows_examined, sql_text 
   FROM mysql.slow_log 
   WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 HOUR) 
   ORDER BY query_time DESC LIMIT 10;"
   ```

## 监控指标

### 关键指标

1. **容器健康状态**: php-web-yinji, php-web-yinji-admin 容器Running状态
2. **响应时间**: Web服务响应时间 < 5秒
3. **磁盘使用**: 数据盘使用率 < 80%
4. **音频文件完整性**: 音频文件可正常访问
5. **微信接口**: access_token有效性

### 监控命令

```bash
# 容器状态监控
docker ps | grep yinji | grep -c "Up"

# 磁盘空间监控
df -h /mnt/datadisk0 | awk 'NR==2 {print $5}' | sed 's/%//'

# 服务响应监控
curl -w "%{time_total}" -o /dev/null -s http://5iyinji.nmzyb.cn/health

# 音频文件监控
find /mnt/datadisk0/volumns/php-web/yinji/yinji-admin/public/wechat/m4a/ -name "*.m4a" -mtime -1 | wc -l
```

## 备份策略

### 数据备份
- **数据库**: 每日凌晨3点自动备份
- **音频文件**: 每周增量备份到NFS存储
- **代码**: Git仓库备份
- **配置文件**: 每日备份

### 备份脚本
```bash
#!/bin/bash
# yinji业务备份脚本
DATE=$(date +%Y%m%d)
BACKUP_DIR="/mnt/datadisk0/backups/yinji/$DATE"
mkdir -p $BACKUP_DIR

# 数据库备份
docker exec mysql-prod mysqldump -u root -p'56d9DavJ*zwrwj9rmA' --single-transaction --routines --triggers yinji > $BACKUP_DIR/yinji.sql
docker exec mysql-prod mysqldump -u root -p'56d9DavJ*zwrwj9rmA' --single-transaction --routines --triggers yinji_admin > $BACKUP_DIR/yinji_admin.sql

# 配置备份
cp -r /mnt/datadisk0/apps/php-web/yinji $BACKUP_DIR/

# 增量备份音频文件（仅备份最近7天的文件）
find /mnt/datadisk0/volumns/php-web/yinji/yinji-admin/public/wechat/m4a/ -name "*.m4a" -mtime -7 -exec cp {} $BACKUP_DIR/audio_backup/ \;

echo "yinji backup completed: $BACKUP_DIR"
```

## 性能优化建议

### 数据库优化
```sql
-- 添加索引优化查询性能
ALTER TABLE user_activity ADD INDEX idx_created_at (created_at);
ALTER TABLE wechat_api_log ADD INDEX idx_created_at (created_at);
ALTER TABLE audio_files ADD INDEX idx_status_created (status, created_at);
```

### 缓存优化
```bash
# 设置Redis内存使用策略
docker exec redis-prod redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

### 文件存储优化
```bash
# 音频文件按日期目录存储
mkdir -p /mnt/datadisk0/volumns/php-web/yinji/yinji-admin/public/wechat/m4a/$(date +%Y%m)
```

## 更新记录

| 日期 | 版本 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 2025-07-29 | v1.0 | 初始版本创建 | Claude |

---
**维护负责人**: 运维团队  
**紧急联系**: 技术支持部门  
**特别注意**: 该系统包含大量音频文件，操作前请确保备份安全