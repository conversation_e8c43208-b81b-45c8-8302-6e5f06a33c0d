# hyqm项目迁移计划

**创建时间**: 2025-07-25 21:00
**执行人**: AI Assistant

## 📋 迁移概述

将hyqm项目从旧服务器迁移到腾讯云服务器。hyqm_prod和hyqm_qm实际上是同一个项目的两个前端版本，通过不同域名提供服务（hyqm.nmzyb.cn 和 qm.nmzyb.cn），共享同一个后端服务和数据库，采用单容器双端口部署方案。

## 🎯 迁移目标

### 项目架构澄清
- **hyqm_prod** 和 **hyqm_qm** 是同一个项目的两个前端版本
- **域名分离**: hyqm.nmzyb.cn（主版本）和 qm.nmzyb.cn（无logo版本）
- **共享后端**: 同一个yudao-server.jar，监听48080端口
- **共享数据库**: 同一个hyqm数据库
- **差异**: 仅前端静态文件不同

### 部署配置（单容器双端口方案）
- **容器**: 1个zhao0829wang/java-nginx:1.0容器
- **端口配置**: 
  - nginx 80端口 → 外部8086端口 → WAF转发hyqm.nmzyb.cn
  - nginx 8087端口 → 外部8087端口 → WAF转发qm.nmzyb.cn
  - Java后端48080端口（容器内部，两个前端共享）
- **镜像**: zhao0829wang/java-nginx:1.0
- **数据库**: hyqm（共享）

## 📊 基础架构分析

### 项目架构（与imusti相同）
- **后端**: Spring Boot应用 (yudao-server.jar)
- **前端**: Vue.js静态文件
- **容器化**: Docker + Nginx前端代理

### 目录结构（单容器双端口模式）
```
/mnt/datadisk0/apps/java-web/
└── hyqm/                 # 统一的项目目录
    ├── conf/
    │   └── nginx.conf         # 双端口nginx配置
    ├── docker-compose.yml     # 单容器配置
    └── .env

/mnt/datadisk0/volumns/java-web/
└── hyqm/
    ├── workdir/          # 共享的JAR文件和上传文件
    ├── ui-prod/          # hyqm_prod前端静态文件（nginx 80端口）
    ├── ui-qm/            # hyqm_qm前端静态文件（nginx 8087端口）
    └── logs/             # 共享的应用日志
```

## 🚀 hyqm项目迁移详细计划（统一迁移）

### 第一阶段：数据库迁移
**目标**: 导入hyqm数据库到mysql-prod容器
- 导出214服务器的hyqm数据库
- 通过208中转压缩传输
- 导入到腾讯云mysql-prod容器


#### 步骤1：导出hyqm数据库
```bash
# 在208服务器执行
mysqldump -h 200.1.66.214 -u root -p'TxkjDB2020#' \
  --single-transaction --routines --triggers hyqm > /tmp/hyqm.sql

# 检查文件大小
ls -lh /tmp/hyqm.sql
```

#### 步骤2：压缩并传输
```bash
# 压缩文件
tar -czf /usr/share/nginx/html/transfer/hyqm.sql.tar.gz /tmp/hyqm.sql

# 腾讯云下载
wget -O /tmp/hyqm.sql.tar.gz https://main.51zsqc.com/transfer/hyqm.sql.tar.gz
```

#### 步骤3：数据库导入
```bash
# 解压并复制到容器
cd /tmp && tar -xzf hyqm.sql.tar.gz
docker cp /tmp/hyqm.sql mysql-prod:/tmp/

# 创建数据库
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' \
  -e "CREATE DATABASE IF NOT EXISTS hyqm DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 导入数据（如遇GTID问题需清理）
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' hyqm < /tmp/hyqm.sql

# 如果遇到GTID错误，执行清理后重新导入
docker exec mysql-prod bash -c "sed '/-- GTID state at the end of the backup/,/[0-9]*.*;$/d' /tmp/hyqm.sql > /tmp/hyqm_clean.sql"
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' --force hyqm < /tmp/hyqm_clean.sql
```

#### 步骤4：清理临时文件
```bash
# 208服务器清理
rm /usr/share/nginx/html/transfer/hyqm.sql.tar.gz
rm /tmp/hyqm.sql

# 腾讯云服务器清理
rm /tmp/hyqm.sql.tar.gz /tmp/hyqm.sql
docker exec mysql-prod rm /tmp/hyqm.sql /tmp/hyqm_clean.sql
```

### 第二阶段：应用文件迁移
**目标**: 获取hyqm_prod和hyqm_qm的所有文件
- hyqm_prod: 已完成传输并解压到/tmp/hyqm_prod/
- hyqm_qm: 需要从210服务器获取

### 第三阶段：容器配置（单容器双端口）

#### 3.1 目录结构创建
```bash
mkdir -p /mnt/datadisk0/apps/java-web/hyqm/{conf,logs,scripts}
mkdir -p /mnt/datadisk0/volumns/java-web/hyqm/{workdir,ui-prod,ui-qm,logs}
```

#### 3.2 文件部署
```bash
# 后端文件（共享）
cp -r /tmp/hyqm_prod/java/* /mnt/datadisk0/volumns/java-web/hyqm/workdir/
mkdir -p /mnt/datadisk0/volumns/java-web/hyqm/workdir/upload

# 前端文件（分别部署到不同目录）
cp -r /tmp/hyqm_prod/vue/* /mnt/datadisk0/volumns/java-web/hyqm/ui-prod/
cp -r /tmp/hyqm_qm/vue/* /mnt/datadisk0/volumns/java-web/hyqm/ui-qm/
```

#### 3.3 应用配置分析（关键步骤）
- 解压JAR：`unzip -q yudao-server.jar -d tmp/`
- 分析配置文件：查看application-*.yaml
- **确定数据库配置**: 从配置文件分析真实的数据库连接
- **确定Spring profile**: 基于配置文件确定正确的profile

#### 3.4 数据库用户权限配置
- 根据配置文件分析结果配置数据库权限
- 参照imusti模式创建专用数据库用户

#### 3.5 创建docker-compose.yml（单容器双端口）
**核心配置**:
```yaml
version: '3.8'

networks:
  1panel-network:
    external: true

services:
  hyqm:
    image: zhao0829wang/java-nginx:1.0
    container_name: hyqm
    networks:
      - 1panel-network
    ports:
      - "8086:80"    # hyqm_prod前端（hyqm.nmzyb.cn）
      - "8087:8087"  # hyqm_qm前端（qm.nmzyb.cn）
    volumes:
      - /mnt/datadisk0/volumns/java-web/hyqm/workdir:/workdir
      - /mnt/datadisk0/volumns/java-web/hyqm/ui-prod:/home/<USER>/projects/ruoyi-ui
      - /mnt/datadisk0/volumns/java-web/hyqm/ui-qm:/home/<USER>/projects/ruoyi-ui-qm
      - /mnt/datadisk0/volumns/java-web/hyqm/logs:/home/<USER>/logs
      - /mnt/datadisk0/apps/java-web/hyqm/conf/nginx.conf:/etc/nginx/nginx.conf:ro
    environment:
      - TZ=Asia/Shanghai
      - COMMAND_NAME=-jar yudao-server.jar --spring.profiles.active=prod --ruoyi.profile=/workdir/upload --spring.datasource.dynamic.datasource.master.url=********************************* --spring.redis.host=redis-prod --spring.rabbitmq.host=rabbitmq-prod --server.port=48080
    restart: unless-stopped
```

#### 3.6 创建nginx配置文件（双端口配置）
**nginx.conf（双server块配置）**:
```nginx
worker_processes  1;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;
    
    # 开启gzip压缩
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 5;
    gzip_types text/plain application/x-javascript text/css application/xml application/javascript;
    
    client_max_body_size 200m;

    # 第一个服务器块 - hyqm_prod（端口80，对应外部8086）
    server {
        listen       80;
        server_name  localhost;

        location / {
            root   /home/<USER>/projects/ruoyi-ui;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        location /prod-api/ {
            proxy_pass http://localhost:48080/;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location /admin-api/ {
            proxy_pass http://localhost:48080/admin-api/;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location /app-api/ {
            proxy_pass http://localhost:48080/app-api/;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    }

    # 第二个服务器块 - hyqm_qm（端口8087，对应外部8087）
    server {
        listen       8087;
        server_name  localhost;

        location / {
            root   /home/<USER>/projects/ruoyi-ui-qm;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        location /prod-api/ {
            proxy_pass http://localhost:48080/;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location /admin-api/ {
            proxy_pass http://localhost:48080/admin-api/;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location /app-api/ {
            proxy_pass http://localhost:48080/app-api/;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    }
}
```

#### 3.7 RabbitMQ队列配置
- 先启动容器，观察日志中的队列错误信息
- 根据实际错误创建所需的队列（每个应用队列名称可能不同）
- 队列创建命令：`docker exec rabbitmq-prod rabbitmqadmin -u hyxx -p hyxx123 declare queue name=[实际队列名] durable=true`

### 第四阶段：启动和验证

#### 4.1 容器启动
```bash
cd /mnt/datadisk0/apps/java-web/hyqm
docker compose up -d
```

#### 4.2 问题排查（参照imusti经验）
- 监控启动日志：`docker logs hyqm`
- 数据库连接问题处理
- RabbitMQ队列问题处理
- 端口冲突处理

#### 4.3 功能验证
- hyqm_prod前端：http://服务器IP:8086
- hyqm_qm前端：http://服务器IP:8087
- 后端API测试（两个前端共享）
- 数据库连接验证

## 📊 单容器双端口方案优势

### 架构优势
- **资源节约**: 1个容器同时服务两个前端，共享Java后端
- **数据一致**: 共享数据库和后端逻辑，确保数据一致性
- **维护简单**: 只需维护1个Java应用和1个容器
- **配置统一**: nginx单文件配置，便于管理
- **扩展灵活**: 可轻易添加更多端口和前端版本

### WAF转发架构
```
hyqm.nmzyb.cn → WAF → 8086端口 → 容器80端口 → /home/<USER>/projects/ruoyi-ui
qm.nmzyb.cn → WAF → 8087端口 → 容器8087端口 → /home/<USER>/projects/ruoyi-ui-qm
两个前端共享 → 容器内部48080端口 → Java应用
```

## ⚠️ 关键注意事项

### 数据库配置分析优先级
1. **第一步**: 解压JAR分析application配置文件
2. **第二步**: 确定正确的数据库连接配置
3. **第三步**: 根据配置导入对应数据库
4. **禁止**: 直接推断数据库配置

### 参照imusti成功模式采用优化架构
- **镜像**: zhao0829wang/java-nginx:1.0（已验证多端口支持）
- **网络**: 1panel-network
- **架构**: 单容器双端口模式
- **配置**: nginx双server块配置
- **启动流程**: 严格按照imusti的解决问题方式

### 端口分配（单容器双端口）
- **容器外部**: 8086（hyqm_prod）、8087（hyqm_qm）
- **容器内部**: 80（hyqm_prod）、8087（hyqm_qm）、48080（Java后端）
- **WAF转发**: 域名→端口→容器内nginx→共享Java应用