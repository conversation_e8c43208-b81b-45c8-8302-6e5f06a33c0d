# 2025-07-29 汇远轻媒2.0测试环境前端迁移记录

**迁移时间**: 2025-07-29  
**迁移内容**: 汇远轻媒2.0测试环境前端静态文件迁移  
**源环境**: 200.1.66.208:/usr/share/nginx/html/hyqm2-test  
**目标环境**: 腾讯云1Panel + OpenResty (test-hyqm2.nmzyb.cn)  

## 背景

基于生产环境前端迁移的成功经验，对测试环境进行同样的前端迁移改造。测试环境后端容器 `hyqm2-test` 运行在端口9005，需要将原来的全路径反向代理改造为静态文件 + API代理的混合模式。

## 核心技术方案

### 架构对比

**迁移前（纯反向代理）**：
```
外部请求 → 雷池WAF → 1Panel OpenResty → 全路径代理 → hyqm2-test:9005
```

**迁移后（混合模式）**：
```
外部请求 → 雷池WAF → 1Panel OpenResty → {
    ├── 静态文件请求 → 直接返回 (/www/sites/test-hyqm2.nmzyb.cn/index/)
    └── API请求 (/api/*) → 反向代理 → hyqm2-test:9005
}
```

### 关键配置变更

**原配置 (root.conf)**：
```nginx
location ^~ / {
    proxy_pass http://127.0.0.1:9005;
    # ... 全路径代理到后端
}
```

**新配置结构**：
- `api.conf`: API专用代理配置
- `static.conf`: 静态文件服务配置  
- `root.conf.backup`: 原配置备份

## 详细实施过程

### 步骤1：文件传输
```bash
# 208服务器打包测试环境前端
cd /usr/share/nginx/html
tar -czf hyqm2-test-frontend.tar.gz hyqm2-test
mv hyqm2-test-frontend.tar.gz /usr/share/nginx/html/transfer/

# 腾讯云下载
wget -T 30 https://main.51zsqc.com/transfer/hyqm2-test-frontend.tar.gz -O /tmp/hyqm2-test-frontend.tar.gz
```

### 步骤2：部署静态文件
```bash
# 创建目录结构
mkdir -p /opt/1panel/www/sites/test-hyqm2.nmzyb.cn/index

# 解压部署
cd /tmp && tar -xzf hyqm2-test-frontend.tar.gz
sudo cp -r /tmp/hyqm2-test/* /opt/1panel/www/sites/test-hyqm2.nmzyb.cn/index/
```

### 步骤3：配置文件重构

#### 3.1 备份原配置
```bash
sudo mv /opt/1panel/www/sites/test-hyqm2.nmzyb.cn/proxy/root.conf \
       /opt/1panel/www/sites/test-hyqm2.nmzyb.cn/proxy/root.conf.backup
```

#### 3.2 创建API代理配置
文件：`/opt/1panel/www/sites/test-hyqm2.nmzyb.cn/proxy/api.conf`
```nginx
# API反向代理配置 - 汇远轻媒2.0测试环境
location ^~ /api/ {
    proxy_pass http://127.0.0.1:9005; 
    proxy_set_header Host $host; 
    proxy_set_header X-Real-IP $remote_addr; 
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for; 
    proxy_set_header REMOTE-HOST $remote_addr; 
    proxy_set_header Upgrade $http_upgrade; 
    proxy_set_header Connection $http_connection; 
    proxy_set_header X-Forwarded-Proto $scheme; 
    proxy_set_header X-Forwarded-Port $server_port; 
    proxy_http_version 1.1; 
    add_header X-Cache $upstream_cache_status; 
    add_header Cache-Control no-cache; 
    proxy_ssl_server_name off; 
    proxy_ssl_name $proxy_host; 
}
```

#### 3.3 创建静态文件服务配置
文件：`/opt/1panel/www/sites/test-hyqm2.nmzyb.cn/proxy/static.conf`
```nginx
# 静态文件服务配置 - 汇远轻媒2.0测试环境
location / {
    root /www/sites/test-hyqm2.nmzyb.cn/index;
    index index.html index.htm;
    try_files $uri $uri/ /index.html;
    
    # 静态资源缓存配置
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # HTML文件不缓存，确保及时更新
    location ~* \.(html|htm)$ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
    }
}
```

### 步骤4：配置生效
```bash
# 验证配置语法
docker exec 1Panel-openresty-YFc6 openresty -t

# 重新加载配置
docker exec 1Panel-openresty-YFc6 openresty -s reload
```

### 步骤5：功能验证
```bash
# 前端页面验证
curl -I http://test-hyqm2.nmzyb.cn/
# HTTP/1.1 200 OK, Content-Length: 1288 ✅

# 静态资源验证  
curl -I http://test-hyqm2.nmzyb.cn/favicon.ico
# HTTP/1.1 200 OK, Content-Length: 16958 ✅

# API代理验证
curl -I http://test-hyqm2.nmzyb.cn/api/
# HTTP/1.1 404 Not Found (后端正常响应) ✅
```

## 技术要点总结

### 配置优先级处理
由于测试环境原来是全路径代理 (`location ^~ /`)，与静态文件服务 (`location /`) 存在冲突，需要：
1. **备份原配置**：保留原始配置以备回滚
2. **精确路径匹配**：API使用 `^~ /api/` 前缀匹配
3. **通用路径匹配**：静态文件使用 `/` 普通匹配

### 与生产环境的差异
| 项目 | 生产环境 | 测试环境 |
|------|----------|----------|
| 域名 | hyqm2.nmzyb.cn | test-hyqm2.nmzyb.cn |
| 后端端口 | 9006 | 9005 |
| 原配置 | API专用代理 | 全路径代理 |
| 配置文件 | `root.conf` + `static.conf` | `api.conf` + `static.conf` |
| 迁移难度 | 简单（补充配置） | 中等（重构配置） |

### 回滚方案
如需回滚到原纯代理模式：
```bash
# 恢复原配置
sudo mv /opt/1panel/www/sites/test-hyqm2.nmzyb.cn/proxy/root.conf.backup \
       /opt/1panel/www/sites/test-hyqm2.nmzyb.cn/proxy/root.conf

# 删除新配置
sudo rm /opt/1panel/www/sites/test-hyqm2.nmzyb.cn/proxy/api.conf
sudo rm /opt/1panel/www/sites/test-hyqm2.nmzyb.cn/proxy/static.conf

# 重新加载
docker exec 1Panel-openresty-YFc6 openresty -s reload
```

## 最终配置结构

```
/opt/1panel/www/sites/test-hyqm2.nmzyb.cn/
├── index/                    # 前端静态文件目录
│   ├── index.html           # 主页面文件
│   ├── assets/              # 编译后的资源
│   ├── js/                  # JavaScript文件
│   ├── favicon.ico          # 网站图标
│   └── ...                  # 其他静态资源
├── proxy/                   # nginx配置文件目录
│   ├── api.conf            # API反向代理配置（新建）
│   ├── static.conf         # 静态文件服务配置（新建）
│   └── root.conf.backup    # 原全路径代理配置（备份）
├── log/                    # 访问日志
│   ├── access.log          # 访问日志
│   └── error.log           # 错误日志
└── ssl/                    # SSL证书目录
```

## 性能优化效果

### 访问性能提升
- **静态资源**：直接由OpenResty提供，无需经过Python应用，响应速度显著提升
- **缓存策略**：静态资源1年缓存，减少重复请求
- **日志优化**：静态资源不记录访问日志，减少I/O开销

### 服务器资源节约
- **后端负载**：静态文件请求不再占用Python应用进程
- **内存使用**：减少不必要的代理转发开销
- **网络带宽**：优化的缓存策略减少重复传输

## 故障排查记录

### 问题1：配置冲突
**现象**：添加静态配置后，API请求仍然返回静态文件  
**原因**：原 `location ^~ /` 与新 `location /` 存在匹配冲突  
**解决**：备份并删除原全路径代理配置，创建专用API代理配置

### 问题2：文件权限
**现象**：静态文件复制失败，权限被拒绝  
**原因**：目标目录需要root权限  
**解决**：使用 `sudo cp` 命令复制文件

## 清理工作

### 临时文件清理
```bash
# 208服务器清理
rm /usr/share/nginx/html/transfer/hyqm2-test-frontend.tar.gz

# 腾讯云服务器清理  
rm /tmp/hyqm2-test-frontend.tar.gz
rm -rf /tmp/hyqm2-test
```

## 迁移总结

**✅ 迁移成果**：
1. **功能完整性**：前端页面和API接口均正常工作
2. **性能优化**：静态文件访问性能显著提升
3. **架构统一**：与生产环境保持一致的技术架构
4. **配置可维护**：清晰的配置文件结构，便于后续维护

**✅ 技术创新**：
1. **配置重构**：成功将全路径代理改造为混合服务模式
2. **平滑迁移**：保留原配置备份，支持快速回滚
3. **最佳实践**：建立了测试环境迁移的标准流程

**✅ 经验积累**：
1. **1Panel配置机制**：深入理解了配置文件包含和加载机制
2. **Nginx路径匹配**：掌握了复杂场景下的location配置技巧
3. **容器网络**：熟悉了容器间通信和路径映射

---

**总结**：本次测试环境迁移在生产环境成功经验的基础上，进一步完善了迁移方案，特别是处理了配置冲突和架构重构的复杂场景，为后续项目迁移提供了更加完整的技术方案和操作指南。