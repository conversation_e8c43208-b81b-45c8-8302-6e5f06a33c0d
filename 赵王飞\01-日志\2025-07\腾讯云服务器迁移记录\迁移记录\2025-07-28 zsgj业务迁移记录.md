# 2025-07-28 掌上青城（zsgj/zsqc）业务迁移记录

## 业务概述

### 项目生态系统
掌上青城是一个完整的城市服务生态系统，包含多个子项目：

#### 核心项目
1. **zsgj** (4.2G) - 掌上青城主应用
   - 项目类型: PHP ThinkPHP 3.x应用
   - 项目位置: /usr/share/nginx/html/zsgj
   - 环境配置: product_yun

2. **zsqc-admin** (2.3G) - 管理后台
   - 项目类型: 海豚（Dolphin）框架应用
   - 项目位置: /usr/share/nginx/html/zsqc-admin

#### 功能模块
3. **zsqc_shop** (261M) - 商城模块
4. **zsqc-usercenter** (38M) - 用户中心
5. **zsqc-mapline** (37M) - 地图线路服务
6. **zsqc-icbc** (32M) - 工商银行服务集成
7. **zsqc-app** (23M) - APP相关服务
8. **zsqc-static-admin** (12M) - 静态资源管理
9. **zsqc-ysf** (6.1M) - 易生活服务
10. **zsqc-garbage** (4.4M) - 垃圾分类
11. **zsqc-hsjc** (2.8M) - 核酸检测
12. **zsqc-epidemic** (2.4M) - 疫情防控
13. **zsqc-subway** (2.0M) - 地铁服务
14. **zsqc** (1.6M) - 静态前端页面

### 技术栈
- **Web服务器**: Nginx
- **应用语言**: 
  - zsgj主项目: PHP 5.6 (ThinkPHP 3.x)
  - 其他PHP项目: PHP 7.2 (Dolphin框架等)
- **数据库**: MySQL
- **缓存**: Redis + Memcache（腾讯云已有memcached-prod容器）
- **环境配置**: product_yun / product
- **Docker镜像**:
  - zsgj: zhao0829wang/apache2-php:5.6-rc2
  - 其他PHP项目: zhao0829wang/apache2-php:7.2-rc4

### 数据库架构
#### 主要数据库
1. **zsgj** - 掌上青城主数据库
2. **zsqc-admin** - 管理后台数据库
3. **shop-zsqc** - 商城数据库
4. **zsqc_cal_data** - 计算数据库
5. **zsqc_garbage** - 垃圾分类数据库
6. **zsqc_subway** - 地铁数据库

#### 日志数据库
7. **zsgj_bus_log** - 业务日志
8. **zsgj_log** - 系统日志
9. **zsgjlog** - 其他日志

### 外部依赖
- Redis服务器: 200.1.66.209, 200.1.66.208:30079
- Memcache服务器: 200.1.66.209:11211
- 数据库服务器: 200.1.66.214

## 迁移规划

### 迁移目标
将掌上青城完整生态系统从旧服务器(200.1.66.208)迁移到腾讯云服务器，实现容器化部署。

### 迁移策略
由于项目规模庞大（总计约7G+），采用分阶段迁移策略：
1. 先迁移数据库（9个数据库）
2. 迁移核心应用（zsgj主应用）
3. 迁移管理后台（zsqc-admin）
4. 逐步迁移各功能模块
5. 建立统一的容器化架构

### 迁移阶段

#### 阶段一：环境准备和分析（预估2小时）
- [ ] 分析所有项目技术栈和依赖关系
- [ ] 评估数据库大小和迁移策略
- [ ] 确定各项目的域名和访问路径
- [ ] 制定容器化架构方案

#### 阶段二：数据库迁移（预估3小时）
##### 主要数据库
- [ ] 备份并迁移zsgj主数据库
- [ ] 备份并迁移zsqc-admin管理后台数据库
- [ ] 备份并迁移shop-zsqc商城数据库
- [ ] 备份并迁移zsqc_cal_data计算数据库
- [ ] 备份并迁移zsqc_garbage垃圾分类数据库
- [ ] 备份并迁移zsqc_subway地铁数据库

##### 日志数据库
- [ ] 备份并迁移zsgj_bus_log业务日志库
- [ ] 备份并迁移zsgj_log系统日志库
- [ ] 备份并迁移zsgjlog其他日志库

#### 阶段三：核心应用迁移（预估3小时）
- [ ] 分析zsgj (4.2G)大文件分布
- [ ] 打包并传输核心代码
- [ ] 处理大文件和静态资源
- [ ] 配置容器化环境
- [ ] 验证核心功能

#### 阶段四：管理后台迁移（预估2小时）
- [ ] 分析zsqc-admin (2.3G)项目结构
- [ ] 打包并传输管理后台代码
- [ ] 配置海豚框架环境
- [ ] 验证后台管理功能

#### 阶段五：功能模块迁移（预估3小时）
- [ ] 迁移zsqc_shop商城模块
- [ ] 迁移zsqc-usercenter用户中心
- [ ] 迁移zsqc-mapline地图服务
- [ ] 迁移zsqc-icbc工行服务
- [ ] 迁移其他小型模块（app、ysf、garbage等）

#### 阶段六：集成验证（预估2小时）
- [ ] 配置统一反向代理
- [ ] 验证所有模块互联互通
- [ ] 性能测试和优化
- [ ] 制定运维方案

### 预计总时长：15小时（建议分2-3天完成）

## 技术要点

### 配置修改
1. 数据库连接配置
   - 从200.1.66.214改为mysql-prod容器
   - 更新数据库密码为容器密码

2. 缓存服务配置
   - Redis: 从200.1.66.209改为redis-prod容器
   - Memcache: 从200.1.66.209:11211改为memcached-prod容器

3. 环境配置文件修改
   - zsgj/env/product_yun.php
   - zsqc-admin/env/product.php
   - 各子模块的配置文件

### 容器化架构设计

#### 1. zsgj主应用容器（PHP 5.6）
```yaml
php-web-zsgj:
  image: zhao0829wang/apache2-php:5.6-rc2
  container_name: html-51zsqc-zsgj
  networks:
    - 1panel-network
  volumes:
    - /mnt/datadisk0/volumns/php-web/zsqc/zsgj:/var/www/html
    - /mnt/datadisk0/volumns/php-web/zsqc/zsgj-logs:/var/log/apache2
  environment:
    - DB_HOST=mysql-prod
    - REDIS_HOST=redis-prod
    - MEMCACHE_HOST=memcached-prod
```

#### 2. 其他PHP应用容器（PHP 7.2）
```yaml
php-web-zsqc-apps:
  image: zhao0829wang/apache2-php:7.2-rc4
  container_name: php-web-zsqc-apps
  networks:
    - 1panel-network
  volumes:
    - /mnt/datadisk0/volumns/php-web/zsqc/zsqc-admin:/var/www/html/zsqc-admin
    - /mnt/datadisk0/volumns/php-web/zsqc/modules:/var/www/html/modules
    - /mnt/datadisk0/volumns/php-web/zsqc/apps-logs:/var/log/apache2
  environment:
    - DB_HOST=mysql-prod
    - REDIS_HOST=redis-prod
    - MEMCACHE_HOST=memcached-prod
```

### 统一访问架构
**域名**: zsqc.51zsqc.com

```
用户请求 → 雷池WAF → 1Panel OpenResty → {
    ├─ /zsgj/* → html-51zsqc-zsgj容器 (PHP 5.6)
    └─ /zsqc-*/* → php-web-zsqc-apps容器 (PHP 7.2)
}
```

**路径分流规则**:
- **PHP 5.6处理**: 所有 `/zsgj/*` 路径 → `html-51zsqc-zsgj`
- **PHP 7.2处理**: 所有 `/zsqc-*/*` 路径 → `php-web-zsqc-apps`
  - 包括：zsqc-admin、zsqc-shop、zsqc-mapline、zsqc-icbc等所有zsqc模块

## 数据分析结果

### 数据库大小分析
| 数据库 | 大小 | 说明 |
|--------|------|------|
| **zsgj** | 4.5GB | 主数据库，需要特殊处理 |
| **zsqc_cal_data** | 2.4GB | 计算数据，体积较大 |
| **zsqc-admin** | 2.1GB | 管理后台数据库 |
| zsgjlog | 317MB | 日志数据库 |
| zsgj_bus_log | 43MB | 业务日志 |
| zsgj_log | 13MB | 系统日志 |
| shop-zsqc | 5MB | 商城数据库 |
| zsqc_garbage | 0.2MB | 垃圾分类 |
| zsqc_subway | 0.03MB | 地铁数据 |

**总计**: 约9.4GB数据库数据

### 大文件分布分析
#### zsgj项目（4.2G）
- **Public/portrait**: 3.4G（用户头像等图片）
- **Public/apk**: 853M（APP安装包）
- **其他代码**: 约500M

#### zsqc-admin项目（2.3G）
- **runtime**: 1.5G（运行时文件，可清理）
- **public**: 717M（静态资源）
- **其他代码**: 约100M

### 迁移策略调整
1. **数据库分批迁移**: 
   - 先迁移小数据库（<100MB）
   - 单独处理大数据库（zsgj、zsqc_cal_data、zsqc-admin）
   
2. **文件传输策略**:
   - zsgj: 分离portrait和apk目录单独传输
   - zsqc-admin: 清理runtime目录后再传输

## 风险评估

### 高风险
1. **超大数据库迁移**: 3个数据库超过2GB，需要优化传输策略
2. **海量图片文件**: 3.4G的用户头像需要分批传输
3. **PHP版本差异**: zsgj使用PHP 5.6，其他使用PHP 7.2

### 中风险
1. **PHP版本兼容性**: ThinkPHP 3.x可能对PHP版本有特定要求
2. **硬编码配置**: 可能存在硬编码的IP地址需要修改
3. **日志路径**: 日志写入路径需要适配容器环境

### 低风险
1. **域名配置**: 需要确认原有访问域名
2. **定时任务**: 可能存在需要迁移的cron任务

## 回滚方案
1. 保留原服务器上的应用和数据
2. 通过DNS或反向代理快速切换
3. 数据库备份可快速恢复

## 注意事项
1. 迁移过程中保持原服务正常运行
2. 分批传输大文件，避免网络拥堵
3. 注意清理传输过程中的临时文件
4. 详细记录每个步骤的操作和结果

---

## 迁移执行记录

### 当前状态 (2025-07-28 17:00)

#### ✅ 已完成项目
1. **数据库迁移**: 9个数据库全部迁移完成，总计9.4GB
   - zsgj (4.5GB)、zsqc_cal_data (2.4GB)、zsqc-admin (2.1GB)
   - 其他6个小数据库全部完成
   
2. **应用部署**: 两个容器成功启动
   - `html-51zsqc-zsgj`: PHP 5.6容器，处理zsgj业务
   - `php-web-zsqc-apps`: PHP 7.2容器，处理所有zsqc业务
   
3. **文件传输**: 所有核心文件传输完成
   - 核心代码、管理后台、功能模块
   - 大文件：portrait目录(3.4G)、apk目录(853M)

4. **环境配置**: 
   - 数据库连接配置完成
   - 环境配置文件适配容器环境
   - 数据库权限配置完成

#### 🔄 待完成项目
1. **1Panel反向代理配置**:
   - 域名：zsqc.51zsqc.com
   - 路径分流：/zsgj/* → html-51zsqc-zsgj，/zsqc-*/* → php-web-zsqc-apps
   
2. **功能验证测试**:
   - 数据库连接测试
   - 主要功能模块测试
   - 性能验证

#### 🐳 容器状态
```
CONTAINER ID   IMAGE                               STATUS
html-51zsqc-zsgj    zhao0829wang/apache2-php:5.6-rc2   Up 17 hours
php-web-zsqc-apps   zhao0829wang/apache2-php:7.2-rc4   Up 17 hours
```

#### 📁 目录结构
```
/mnt/datadisk0/
├── apps/php-web/zsqc/              # 容器配置
│   ├── docker-compose-zsgj.yml     # zsgj容器配置
│   └── docker-compose-zsqc-apps.yml # zsqc-apps容器配置
└── volumns/php-web/zsqc/           # 数据存储
    ├── zsgj/                       # zsgj主应用
    ├── zsqc-admin/                 # 管理后台
    └── modules/                    # 功能模块
```