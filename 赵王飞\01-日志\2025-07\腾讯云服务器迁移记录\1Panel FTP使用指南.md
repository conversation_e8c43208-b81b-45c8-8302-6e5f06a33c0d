
## 概述

本指南介绍在1Panel环境下使用FTP服务进行前端文件上传的配置和使用方法。

## 服务器网络环境

### 当前服务器配置
- **内网IP**: ***********
- **FTP服务**: 端口21，已启用
- **被动模式端口**: 21100-21200

### 网络访问方式
通过OpenVPN访问内网IP: ***********

## OpenVPN路由配置

### 客户端路由配置
在客户端OpenVPN配置文件(.ovpn)中添加：
```bash
# 添加到腾讯云内网的路由
route ********** ***********
```

### 连通性测试
```bash
# 测试FTP连通性
telnet *********** 21
```

## FTP服务使用

### 连接信息
- **服务器地址**: ***********
- **端口**: 21
- **协议**: FTP
- **用户名**: 项目专用SFTP用户（在1Panel工具箱-FTP中创建）
- **认证方式**: 密码

### SFTP用户管理
- **创建位置**: 1Panel控制台 → 工具箱 → FTP
- **用户原则**: 每个项目创建一个单独的SFTP用户
- **示例用户**: 
  - 用户名: `hyqm_prod`
  - 密码: `8JpG5hzHHT8ExsDd`
  - 状态: 已启用
  - 根目录: `/opt/1panel/www/sites/hyqm2.nmzyb.cn/index`

### 目录结构
```
/mnt/datadisk0/
├── apps/           # 应用配置
├── volumns/        # 数据存储
│   ├── php_home_208/    # PHP应用文件目录
│   ├── java-web/        # Java应用文件目录
│   └── shared/          # 共享文件目录
├── backups/        # 备份文件
└── logs/          # 日志文件
```

### 常用上传路径
- **1Panel网站目录**: `/opt/1panel/www/sites/[域名]/index/` (前端上传主要目录)
- **PHP项目目录**: `/mnt/datadisk0/volumns/php_home_208/[项目名]/workdir/`
- **静态资源**: `/mnt/datadisk0/volumns/shared/static/`
- **备份文件**: `/mnt/datadisk0/backups/`

## VSCode FTP插件配置

### 推荐插件
- **SFTP**: by Natizyskunk (支持FTP/SFTP)
- **FTP-Sync**: 备选方案

### FTP插件配置示例

#### 1. 安装插件
在VSCode扩展市场搜索"SFTP"安装Natizyskunk的SFTP插件。

#### 2. 项目根目录创建配置
创建 `.vscode/sftp.json` 文件：

```json
{
    "name": "腾讯云服务器",
    "host": "***********",
    "protocol": "ftp",
    "port": 21,
    "username": "hyqm_prod",
    "password": "8JpG5hzHHT8ExsDd",
    "remotePath": "/opt/1panel/www/sites/hyqm2.nmzyb.cn/index",
    "uploadOnSave": true,
    "syncMode": "update",
    "ignore": [
        ".vscode",
        ".git",
        ".DS_Store",
        "node_modules",
        "*.log"
    ],
    "watcher": {
        "files": "**/*",
        "autoUpload": true,
        "autoDelete": false
    }
}
```

#### 3. 多环境配置
```json
{
    "configurations": [
        {
            "name": "开发环境",
            "host": "***********",
            "protocol": "ftp",
            "port": 21,
            "username": "用户名",
            "password": "密码",
            "remotePath": "/mnt/datadisk0/volumns/php_home_208/dev-project/workdir"
        },
        {
            "name": "生产环境",
            "host": "***********",
            "protocol": "ftp",
            "port": 21,
            "username": "用户名",
            "password": "密码",
            "remotePath": "/mnt/datadisk0/volumns/php_home_208/prod-project/workdir"
        }
    ],
    "uploadOnSave": true,
    "syncMode": "update"
}
```

### 常用操作

#### 1. 手动同步
- `Ctrl+Shift+P` → `SFTP: Sync Local -> Remote`
- `Ctrl+Shift+P` → `SFTP: Sync Remote -> Local`

#### 2. 上传文件
- 右键文件 → `Upload`
- 保存文件时自动上传（需开启`uploadOnSave`）

#### 3. 下载文件
- `Ctrl+Shift+P` → `SFTP: Download Active File`
- 右键文件 → `Download`

## 故障排查

### 1. 连接失败
```bash
# 检查FTP服务状态
systemctl status vsftpd

# 检查端口监听
netstat -tulnp | grep :21

# 测试连通性
telnet *********** 21
```

### 2. 路由问题
```bash
# 检查路由表
route -n

# 测试VPN路由
ping ***********
```

### 3. 权限问题
```bash
# 检查目录权限
ls -la /mnt/datadisk0/volumns/

# 修改权限
chmod 755 /mnt/datadisk0/volumns/php_home_208/
```

---

**注意**: 
1. 确保OpenVPN路由配置正确后再进行FTP连接
2. 重要操作前务必备份文件