# 背景
在迁移业务过程中需要了解调用哪些掌上青城的接口，想要参照现在唯一正常运行的云闪付业务，但是发现相关接口无法访问。报错500，需要定位到问题。需要在208服务器通过 nginx 以及阿帕奇等相关日志， 来定位。

**问题接口**:
```
请求网址: https://unionpay.nmzyb.cn/zsqc-admin/public/index.php/app/bus_line/getNearbyStation?lat=40.835578&lng=111.721482
请求方法: GET
状态代码: 500 Internal Server Error
远程地址: 117.161.77.172:443
```

## 问题排查过程

### 1. 初步检查PHP应用日志
在`/usr/share/nginx/html/zsqc-admin/runtime/log/202507/`目录下查看，发现请求后并不会增加日志。

### 2. 检查nginx日志
**nginx访问日志**(`/var/log/nginx/access.log`):
- 请求正常到达nginx
- 被转发到Apache服务器(`************:8080`)
- 返回500状态码

**nginx错误日志**(`/var/log/nginx/error.log`):
- 发现部分"access forbidden by rule"错误，但不是主要原因

### 3. 检查Apache日志
**Apache访问日志**(`/var/log/apache2/access.log`):
- 请求正常到达Apache
- 返回500状态码，响应体392字节

**Apache错误日志**(`/var/log/apache2/error.log`):
发现大量PHP Fatal Error:
```
PHP Fatal error: PDO::__construct(): php_network_getaddresses: getaddrinfo failed: Name or service not known
```

### 4. 根本原因分析
错误信息显示PHP应用无法连接到MySQL数据库，具体是DNS解析失败，无法解析数据库主机名。

### 5. 问题根因确认
原因是208服务器的代码通过SFTP同步时，将腾讯云服务器的配置文件也同步过来了，导致数据库连接配置指向了腾讯云环境的数据库主机名，而208服务器无法解析这些主机名。

## 解决方案

### 执行的修复操作
在`/usr/share/nginx/html/zsqc-admin/`目录下执行:
```bash
git reset --hard
```

**执行结果**:
```
HEAD is now at 01e95656 1.关闭代码，调试开关
```

### 修复原理
`git reset --hard`命令将工作目录完全重置到最后一次提交的状态，丢弃所有未提交的更改，包括通过SFTP同步过来的腾讯云配置文件，恢复到208服务器正常工作的配置状态。

## 总结
- **问题**: 云闪付掌上青城接口返回500错误
- **根因**: SFTP同步导致数据库配置被腾讯云环境配置覆盖
- **现象**: PHP应用无法连接数据库，DNS解析失败
- **解决**: 使用`git reset --hard`恢复到正常工作状态
- **状态**: 问题已修复，接口恢复正常




