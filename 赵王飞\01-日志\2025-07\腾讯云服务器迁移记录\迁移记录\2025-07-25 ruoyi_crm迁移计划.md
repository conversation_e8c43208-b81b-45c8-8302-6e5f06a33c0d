# ruoyi_crm迁移计划

**计划制定时间**: 2025-07-25 18:00
**计划制定人**: AI Assistant

## 1. 项目基本信息

### 1.1 项目概述
- **项目名称**：ruoyi_crm
- **项目类型**：Java Web应用 (Spring Boot + Vue)
- **当前环境**：NFS共享存储（210服务器）
- **目标环境**：Docker容器 + 本地卷挂载
- **迁移优先级**：高（数据量小，适合验证流程）

### 1.2 技术架构
```
前端：Vue.js (静态文件)
后端：Spring Boot (Java应用)
数据库：MySQL (独立数据库)
缓存：Redis (共享)
```

## 2. 迁移范围分析

### 2.1 数据库信息
- **数据库名**: ruoyi_crm
- **表数量**: 66个
- **数据大小**: 15.98 MB
- **源位置**: ************

### 2.2 文件存储分析
**文件位置**：210服务器 `/data/nfs_share/html/HOME_RUOYI_PROD/ruoyi_crm/`
```
ruoyi_crm/
├── java/
│   ├── ruoyi-admin.jar  # 主应用JAR包 (103MB)
│   └── upload/          # 上传文件目录
└── vue/                 # 前端文件
```

### 2.3 存储空间评估
- **源数据库大小**：16MB
- **应用文件大小**：约110MB
- **目标服务器可用空间**：427GB ✅
- **预计传输时间**：5-10分钟

## 3. 迁移前准备工作

### 3.1 环境检查清单
- [ ] 确认208服务器mysqldump工具可用
- [ ] 确认210服务器SSH连接正常
- [ ] 确认腾讯云Docker环境正常
- [ ] 确认mysql-prod容器状态正常
- [ ] 确认redis-prod容器状态正常
- [ ] 确认1panel-network网络连通性
- [ ] 确认https://main.51zsqc.com可访问

### 3.2 镜像和配置确认
- [ ] 确认镜像4525e4716f8b可用
- [ ] 检查ruoyi-admin.jar中的profile
- [ ] 确认端口8083未被占用

## 4. 详细迁移步骤

### 4.1 阶段一：数据库迁移（预计5分钟）

#### 步骤1：备份数据库
```bash
# 在208服务器执行
cd /tmp
mysqldump -h ************ -uroot -pTxkjDB2020# \
  --single-transaction --set-gtid-purged=OFF \
  --routines --triggers ruoyi_crm > ruoyi_crm_$(date +%Y%m%d_%H%M%S).sql

# 检查文件
ls -lh ruoyi_crm_*.sql
```

#### 步骤2：压缩并提供下载
```bash
# 压缩
gzip ruoyi_crm_*.sql

# 移动到Web目录
mv ruoyi_crm_*.sql.gz /usr/share/nginx/html/transfer/

# 记录文件名供后续使用
ls /usr/share/nginx/html/transfer/ruoyi_crm_*.sql.gz
```

#### 步骤3：腾讯云下载并导入
```bash
# 在腾讯云服务器执行
cd /tmp
wget https://main.51zsqc.com/transfer/ruoyi_crm_[时间戳].sql.gz

# 解压
gunzip ruoyi_crm_*.sql.gz

# 复制到容器
docker cp ruoyi_crm_*.sql mysql-prod:/tmp/

# 创建数据库
docker exec mysql-prod mysql -uroot -p56d9DavJ*zwrwj9rmA \
  -e "CREATE DATABASE IF NOT EXISTS ruoyi_crm CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;"

# 导入数据（处理GTID）
docker exec mysql-prod bash -c "sed -i '/^SET @@SESSION.SQL_LOG_BIN/d; /^SET @@GLOBAL.GTID_PURGED/d' /tmp/ruoyi_crm_*.sql"
docker exec mysql-prod bash -c "mysql -uroot -p56d9DavJ*zwrwj9rmA ruoyi_crm < /tmp/ruoyi_crm_*.sql"

# 授权
docker exec mysql-prod mysql -uroot -p56d9DavJ*zwrwj9rmA \
  -e "GRANT ALL PRIVILEGES ON ruoyi_crm.* TO 'pearproject'@'%'; FLUSH PRIVILEGES;"
```

### 4.2 阶段二：应用文件迁移（预计10分钟）

#### 步骤1：打包应用文件（通过208远程执行）
```bash
# 在208服务器上执行远程打包
ssh root@************ "cd /data/nfs_share/html/HOME_RUOYI_PROD/ && tar -czf - ruoyi_crm/" > /usr/share/nginx/html/transfer/ruoyi_crm_files_$(date +%Y%m%d_%H%M%S).tar.gz

# 检查文件
ls -lh /usr/share/nginx/html/transfer/ruoyi_crm_files_*.tar.gz
```

#### 步骤2：腾讯云下载并部署
```bash
# 下载
cd /tmp
wget https://main.51zsqc.com/transfer/ruoyi_crm_files_[时间戳].tar.gz

# 创建目录结构
mkdir -p /mnt/datadisk0/apps/java-web/ruoyi-crm/{conf,logs,scripts}
mkdir -p /mnt/datadisk0/volumns/java-web/ruoyi-crm/{workdir,ui,logs}

# 解压
tar -xzf ruoyi_crm_files_*.tar.gz -C /tmp/

# 部署文件
cp -r /tmp/ruoyi_crm/java/* /mnt/datadisk0/volumns/java-web/ruoyi-crm/workdir/
cp -r /tmp/ruoyi_crm/vue/* /mnt/datadisk0/volumns/java-web/ruoyi-crm/ui/

# 设置权限
chmod -R 755 /mnt/datadisk0/volumns/java-web/ruoyi-crm/
```

### 4.3 阶段三：容器配置和部署（预计10分钟）

#### 步骤1：检查配置
```bash
# 检查可用的profile
cd /mnt/datadisk0/volumns/java-web/ruoyi-crm/workdir/
unzip -l ruoyi-admin.jar | grep "application-.*\.yml" | head -10
```

#### 步骤2：创建Docker Compose配置
```bash
cat > /mnt/datadisk0/apps/java-web/ruoyi-crm/docker-compose.yml << 'EOF'
version: '3.8'

networks:
  1panel-network:
    external: true

services:
  ruoyi-crm:
    image: 4525e4716f8b
    container_name: ruoyi-crm
    networks:
      - 1panel-network
    volumes:
      - /mnt/datadisk0/volumns/java-web/ruoyi-crm/workdir:/workdir
      - /mnt/datadisk0/volumns/java-web/ruoyi-crm/logs:/home/<USER>/logs
      - /mnt/datadisk0/volumns/java-web/ruoyi-crm/ui:/home/<USER>/projects/ruoyi-ui
      - /etc/localtime:/etc/localtime:ro
    environment:
      - TZ=Asia/Shanghai
      - COMMAND_NAME=-jar ruoyi-admin.jar --spring.profiles.active=prod --ruoyi.profile=/workdir/upload --spring.redis.host=redis-prod --spring.redis.port=6379 --spring.datasource.druid.master.url=***************************************************************************************************************************************************
    restart: unless-stopped
    ports:
      - "8084:80"
EOF
```

#### 步骤3：启动服务
```bash
cd /mnt/datadisk0/apps/java-web/ruoyi-crm/
docker compose up -d

# 监控启动日志
docker logs -f ruoyi-crm
```

### 4.4 阶段四：验证和清理（预计5分钟）

#### 步骤1：功能验证
```bash
# 等待应用启动
sleep 30

# 检查容器状态
docker ps | grep ruoyi-crm

# 查看启动日志
docker logs ruoyi-crm --tail 100 | grep -E "(Started|Error|Exception)"

# 测试数据库连接
docker exec mysql-prod mysql -upearproject -pkG7#tPq9@zR2$vX1 \
  -e "USE ruoyi_crm; SHOW TABLES;" | wc -l

# API测试
curl -I http://localhost:8084/
```

#### 步骤2：清理临时文件
```bash
# 先确认服务正常后再清理！

# 208服务器清理
rm -f /usr/share/nginx/html/transfer/ruoyi_crm*.gz
rm -f /tmp/ruoyi_crm*.sql

# 腾讯云清理
rm -f /tmp/ruoyi_crm*
rm -rf /tmp/ruoyi_crm/

# 容器内清理
docker exec mysql-prod rm -f /tmp/ruoyi_crm*.sql
```

## 5. 验收标准

### 5.1 基础验收
- [ ] 容器正常启动并持续运行
- [ ] 日志无严重错误
- [ ] 数据库连接成功
- [ ] Redis连接成功
- [ ] 端口8084可访问

### 5.2 功能验收
- [ ] 应用首页可访问
- [ ] 数据库表数量正确（66个）
- [ ] 基础API响应正常

## 6. 回滚方案

### 6.1 快速回滚步骤
```bash
# 1. 停止容器
docker compose down

# 2. 删除数据库
docker exec mysql-prod mysql -uroot -p56d9DavJ*zwrwj9rmA \
  -e "DROP DATABASE IF EXISTS ruoyi_crm;"

# 3. 删除文件（可选）
# rm -rf /mnt/datadisk0/volumns/java-web/ruoyi-crm/
# rm -rf /mnt/datadisk0/apps/java-web/ruoyi-crm/
```

## 7. 风险和注意事项

### 7.1 已识别风险
1. **端口冲突**：8084端口需要确认未被占用
2. **配置兼容性**：需要验证prod profile是否存在
3. **依赖服务**：可能需要RabbitMQ（根据实际情况调整）

### 7.2 重要提醒
1. **传输安全**：文件下载后立即删除中转文件
2. **验证优先**：先验证功能正常，再清理临时文件
3. **日志监控**：密切关注启动日志中的错误信息

## 8. 时间安排

- **预计总时间**：30分钟
- **建议执行时间**：任何时间（低风险）
- **预留缓冲**：额外30分钟处理可能的问题

这个迁移计划基于标准化流程，修正了文件传输方式，增加了更多验证步骤，确保迁移的成功率。