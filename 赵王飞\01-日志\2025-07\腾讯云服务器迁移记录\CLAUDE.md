# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个腾讯云服务器迁移项目的文档库，记录了从旧服务器集群（200.1.66.x）迁移到腾讯云服务器的完整过程。项目采用Docker容器化架构，使用1Panel作为管理面板，实现现代化的服务器基础设施。

**重要提醒**: 文档中的信息大部分为AI生成，不一定绝对正确，请以实际操作为准。当前主要任务是迁移汇远融媒/汇远轻媒1.0相关业务，已完成部分迁移工作。

## 核心架构

### 服务器环境
- **操作系统**: Ubuntu 24.04 LTS / TencentOS Server 4
- **容器技术**: Docker CE + Docker Compose
- **管理面板**: 1Panel
- **网络架构**: 基于`1panel-network`的容器化网络
- **存储结构**: 数据盘挂载到`/mnt/datadisk0/`

### 关键服务容器
- `mysql-prod`: MySQL 5.7.44 生产数据库
- `redis-prod`: Redis 8.0.3 缓存服务
- `apache_php_service`: Apache2 + PHP 7.2 Web服务
- `1Panel-openresty-YFc6`: OpenResty反向代理网关
- 雷池WAF: Web应用防火墙，作为所有外部请求的入口

### 真实网络架构
```
外部请求 → 雷池WAF → {
    ├─ 直接转发到容器 (部分请求)
    └─ 转发到1Panel OpenResty → 容器 (部分请求)
}
```

### 网络架构原则
- 所有外部访问必须先通过雷池WAF防护
- 雷池WAF内置OpenResty，可直接转发请求到容器或转发给1Panel OpenResty
- 容器间通信使用`1panel-network`网络，通过容器名称（如`mysql-prod`）而非IP地址

## 目录结构

```
/mnt/datadisk0/
├── apps/           # 应用配置管理
│   ├── php-web/    # PHP Web应用配置
│   ├── java-app/   # Java应用配置
│   ├── database/   # 数据库配置
│   └── shared/     # 共享配置
├── volumns/        # 数据存储
│   ├── php_home_208/ # PHP应用数据
│   ├── java-web/   # Java应用数据
│   └── shared/     # 共享数据
├── docker/         # Docker数据
├── backups/        # 备份文件
└── logs/          # 日志文件
```

## 迁移流程标准化

### 标准应用迁移流程
1. **数据库迁移**: 从源环境导出数据库，通过HTTP直传模式传输并导入到`mysql-prod`
2. **应用文件迁移**: 打包应用文件，通过HTTP直传模式传输到目标服务器
3. **容器化配置**: 创建Docker Compose配置，建立标准目录结构
4. **网络配置**: 在1Panel控制台创建网站反向代理，配置雷池WAF规则
5. **服务验证**: 验证数据库连接、API接口、前端页面等功能
6. **临时文件清理**: 清理所有传输过程中的临时文件

### HTTP直传文件传输机制
- **中转平台**: ************服务器（域名：main.51zsqc.com）
- **核心原则**: 
  - 所有备份和打包操作均在中转平台(208)上执行
  - **重要**: 腾讯云无法直接访问内网IP（如************），所有文件必须通过208中转,通过域名访问
- **传输流程**: 
  1. 在208服务器打包文件到`/usr/share/nginx/html/transfer/`
  2. 目标服务器使用`wget https://main.51zsqc.com/transfer/[文件名]`下载
  3. 下载完成后立即删除中转文件

### 文件传输详细流程

#### 迁移项目文件
1. **打包 (在208执行)**: `tar -czf [项目名].tar.gz [项目目录]`
2. **提供下载 (208)**: `mv [项目名].tar.gz /usr/share/nginx/html/transfer/`
3. **下载 (腾讯云)**: `wget https://main.51zsqc.com/transfer/[项目名].tar.gz`
4. **清理 (208)**: `rm /usr/share/nginx/html/transfer/[项目名].tar.gz`
5. **解压部署 (腾讯云)**: `tar -xzf [项目名].tar.gz -C /目标目录/`

#### 迁移数据库
1. **备份 (在208远程执行)**: `mysqldump -h ************ -u root -p'TxkjDB2020#' --single-transaction --routines --triggers [数据库名] > /usr/share/nginx/html/transfer/[数据库名].sql`
2. **下载 (腾讯云)**: `wget https://main.51zsqc.com/transfer/[数据库名].sql`
3. **清理 (208)**: `rm /usr/share/nginx/html/transfer/[数据库名].sql`
4. **导入 (腾讯云)**:
   - 复制到容器: `docker cp [数据库名].sql mysql-prod:/tmp/`
   - 清理GTID: `sed '/-- GTID state at the end of the backup/,/[0-9]*.*;$/d' source.sql > clean.sql`
   - 执行导入: `docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' [数据库名] < /tmp/[数据库名].sql`
5. **最终清理**: 删除腾讯云服务器上及容器内的所有临时文件

## 关键操作命令

### 数据库迁移
```bash
# 从208服务器导出数据库
mysqldump -h ************ -u root -p'TxkjDB2020#' --single-transaction --routines --triggers [数据库名] > [数据库名].sql

# 导入到容器
docker cp [数据库名].sql mysql-prod:/tmp/
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' [数据库名] < /tmp/[数据库名].sql

# 配置权限
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' -e "GRANT ALL PRIVILEGES ON [数据库名].* TO 'pearproject'@'%'; FLUSH PRIVILEGES;"
```

### 应用部署
```bash
# 创建应用目录
mkdir -p /mnt/datadisk0/apps/[应用类型]/[项目名]/{conf,logs,scripts}
mkdir -p /mnt/datadisk0/volumns/[应用类型]/[项目名]/{workdir,ui,logs}

# 启动服务
cd /mnt/datadisk0/apps/[应用类型]/[项目名]/
docker compose up -d
```

## 容器化管理规范

### 命名规范
- **应用实例**: `[项目名]-[环境]` (如 `pearproject-prod`)
- **容器名称**: `[应用类型]-[实例名]` (如 `php-web-pearproject-prod`)
- **网络名称**: 主网络使用`1panel-network`，应用网络使用`[应用类型]-network`

### 安全原则
- 禁止直接暴露端口到公网
- 所有外部访问必须先通过雷池WAF防护
- 雷池WAF根据规则决定直接转发到容器或通过1Panel OpenResty转发
- 使用容器名称进行内部服务通信
- 敏感配置通过.env文件管理

### 标准目录结构
每个应用实例包含：
- `conf/` - 配置文件
- `logs/` - 日志文件  
- `scripts/` - 管理脚本
- `docker-compose.yml` - 容器编排
- `.env` - 环境变量

## Spring Boot应用迁移关键原则

### 配置策略
- **理解优于复制**: 每个应用都是独特的，必须理解配置机制而不是盲目复制
- **Profile优先**: 使用正确的profile（如prod）比大量环境变量覆盖更有效
- **最小化覆盖**: 只覆盖连接信息（数据库、Redis、MQ），不覆盖所有配置

### 正确的配置模式
```yaml
# 推荐：使用命令行参数覆盖
environment:
  - COMMAND_NAME=-jar [jar名] --spring.profiles.active=[profile] --spring.redis.host=redis-prod --spring.datasource.url=***********************/[数据库名]
```

### 避免的错误模式
```yaml
# 避免：环境变量地狱
environment:
  - SPRING_REDIS_HOST=redis-prod
  - SPRING_REDIS_PORT=6379
  - SPRING_DATASOURCE_DRUID_INITIALSIZE=5
  # ... 无穷无尽的配置项
```

## 网站配置

### 反向代理创建
- **唯一方式**: 只能通过1Panel控制台创建网站反向代理
- **注意**: 命令行方式添加应用后，1Panel不会自动加载对应配置，必须重新在控制台创建

## 常见问题解决方案

### GTID冲突处理
```bash
# 清理SQL文件中的GTID设置
sed '/-- GTID state at the end of the backup/,/[0-9]*.*;$/d' source.sql > clean.sql
```

### 数据库名映射
```bash
# 替换SQL文件中的数据库名引用
sed 's/old_db_name\./new_db_name\./g' source.sql > corrected.sql
```

### RabbitMQ队列创建
```bash
# 手动创建所需队列
docker exec rabbitmq-prod rabbitmqctl eval 'rabbit_amqqueue:declare({resource, <<"/">>, queue, <<"队列名">>}, true, false, [], none, <<"hyxx">>).'
```

## 环境凭证

### 旧环境
- 服务器: ************ (中转), ************ (数据库源)
- 数据库Root密码: `TxkjDB2020#`

### 新环境  
- 数据库容器: `mysql-prod`
- 数据库Root密码: `56d9DavJ*zwrwj9rmA`
- 业务数据库用户: `pearproject` / `kG7#tPq9@zR2$vX1`

## 备份策略
- 配置备份: `/mnt/datadisk0/apps/` 目录
- 数据备份: `/mnt/datadisk0/volumns/` 目录  
- 数据库备份: mysqldump导出
- 镜像备份: 关键镜像打包保存

## 通用工作规则

### 文档处理
- 解释性内容直接回复，不写入文档除非明确要求
- 不创建测试脚本除非明确要求
- 有任务文档时，执行后记录：选择原因、实施内容、关键参数配置

### 工具使用
- 使用 mcp-ssh-tencent 管理腾讯云服务器
- 使用 mcp-ssh-208 管理中转服务器
- 使用 mcp-ssh-210 管理NFS存储服务器
- 不使用阻塞命令（如telnet）
- 不删除资源，除非是我创建的

### 迁移执行规范
- 严格按照标准化流程执行迁移
- 每个步骤都要验证结果
- 及时清理临时文件
- 详细记录迁移过程和遇到的问题
- 迁移完成后进行功能验证清单检查

### 计划规范
- 所有计划不要给我规划，预计执行时间, 因为这个时间往往是不够精准的, 没有任何参考意义

### 技术方案制定规范
- **环境理解优先**: 制定任何技术方案前，必须先深入调研和理解现有环境架构、技术栈、运维方式
- **技术栈一致性**: 方案必须与现有技术栈保持一致。如果是容器化环境，所有操作都应基于容器；如果是1Panel管理，应遵循1Panel的管理方式
- **最小变更原则**: 优先选择对现有系统影响最小的方案，避免不必要的架构变更
- **成熟方案优先**: 优先使用经过验证的成熟技术方案，而不是自建或实验性方案
- **逻辑自洽验证**: 方案制定完成后，必须进行自我审查，确保所有步骤逻辑一致，无技术栈混乱
- **一次到位**: 初次提供的方案应该是经过深思熟虑的完整方案，而不是需要多轮修正的草案

### 交互方式以mcp ai-interaction为主
- 有任何问题不合理|任务不清晰|执行过程中方案发生变更等使用MCP进行确认
- 除非我明确说明没有新任务，每次任务完成后调用mcp ai-interaction, 向我请求或确认任务!

这个迁移项目体现了现代化的容器化基础设施建设，通过标准化的流程和规范确保服务的稳定性和可维护性。