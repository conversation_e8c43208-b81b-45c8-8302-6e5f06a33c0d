---
date_created: 2025-07-31 13:49:01
date_modified: 2025-07-31 13:57:30
---
# 1 问题描述
[[1Panel FTP使用指南]]
## 1.1 错误信息
**时间**: 2025-07-31 13:37:20  
**错误类型**: Login authentication failed  
**插件**: VSCode SFTP插件 (Natizyskunk.sftp-1.16.3)

```
[07-31 13:37:20] [error] Error: Login authentication failed
    at g (c:\Users\<USER>\.cursor\extensions\natizyskunk.sftp-1.16.3\dist\extension.js:2:28508)
    at p.<anonymous> (c:\Users\<USER>\.cursor\extensions\natizyskunk.sftp-1.16.3\dist\extension.js:2:29232)
    at p.emit (node:events:524:28)
    at p._write (c:\Users\<USER>\.cursor\extensions\natizyskunk.sftp-1.16.3\dist\extension.js:2:42069)
    ...
```

## 1.2 配置信息
- **服务器**: ***********:21
- **用户名**: hyqm_prod
- **密码**: 8JpG5hzHHT8ExsDd
- **目标路径**: /opt/1panel/www/sites/hyqm2.nmzyb.cn/index

# 2 排查过程

## 2.1 权限检查
### 2.1.1 命令执行
```bash
# 检查目标目录权限
ls -la /opt/1panel/www/sites/hyqm2.nmzyb.cn/index

# 修改目录权限
chmod 755 /opt/1panel/www/sites/hyqm2.nmzyb.cn/index

# 检查上级目录权限
ls -la /opt/1panel/www/sites/hyqm2.nmzyb.cn
```

### 2.1.2 结果分析
- **目标目录**: `/opt/1panel/www/sites/hyqm2.nmzyb.cn/index`
  - 权限: `drwxr-xr-x` (755)
  - 所有者: ubuntu:ubuntu
  - 文件正常，权限充足

- **上级目录**: `/opt/1panel/www/sites/hyqm2.nmzyb.cn`
  - 权限: `drwxr-xr-x` (755)  
  - 所有者: root:root
  - **潜在问题**: 上级目录属于root，可能影响访问

## 2.2 用户状态验证
### 2.2.1 检查结果
- **1Panel用户状态**: hyqm_prod用户状态正常 ✓
- **用户名密码**: 确认无误 ✓  
- **网络连通性**: `telnet *********** 21` 可以正常访问 ✓

## 2.3 初步分析
- 目录权限正常
- 用户账户状态正常  
- 基础网络连通性正常
- **问题定位**: 不是目录权限、用户状态或基础连接问题，可能是**FTP协议层面的认证问题**或**VSCode插件配置问题**。

## 2.4 命令行FTP测试
### 2.4.1 测试命令
```bash
ftp ***********
```

### 2.4.2 测试结果
```
连接到 ***********。
220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------
220-You are user number 1 of 50 allowed.
220-Local time is now 13:55. Server port: 21.
220-This is a private system - No anonymous login
220-IPv6 connections are also welcome on this server.
220 You will be disconnected after 15 minutes of inactivity.
用户(***********:(none)): hyqm_prod
331 User hyqm_prod OK. Password required
530 Login authentication failed
```

### 2.4.3 关键发现
- **FTP服务**: Pure-FTPd [privsep] [TLS]
- **用户名识别**: 服务器接受用户名 hyqm_prod ✓
- **认证失败**: 即使用命令行也出现 `530 Login authentication failed` ❌
- **结论**: **问题确定在服务端**，不是VSCode插件问题

# 3 问题确认与解决

## 3.1 使用腾讯云MCP排查
**时间**: 2025-07-31 14:03-14:05

### 3.1.1 服务状态检查
```bash
ps aux | grep ftp
# 发现 Pure-FTPd 服务正常运行
root 1395535 0.0 0.0 12740 1876 ? Ss 11:43 0:00 pure-ftpd (SERVER)
```

### 3.1.2 用户数据库验证
```bash
pure-pw list
# 用户存在于数据库中
hyqm_prod	/opt/1panel/www/sites/hyqm2.nmzyb.cn/index/./
```

### 3.1.3 日志分析发现关键问题
```bash
sudo journalctl -u pure-ftpd -n 20 --no-pager
```

**关键发现**:
```
Jul 31 13:55:30 VM-0-17-ubuntu pure-ftpd[1731826]: pam_unix(pure-ftpd:auth): check pass; user unknown
Jul 31 13:55:30 VM-0-17-ubuntu pure-ftpd[1731826]: pam_unix(pure-ftpd:auth): authentication failure
```

## 3.2 问题根因确认
### 3.2.1 认证配置检查
```bash
cat /etc/pure-ftpd/conf/PAMAuthentication
# 结果: yes ❌

cat /etc/pure-ftpd/conf/PureDB  
# 结果: /etc/pure-ftpd/pureftpd.pdb ✓

ls -la /etc/pure-ftpd/auth/
# 缺少: 50puredb 链接 ❌
```

**问题根因**: 
1. PAM认证启用，优先于PureDB认证
2. 缺少PureDB认证模块链接

## 3.3 解决方案执行
### 3.3.1 禁用PAM认证
```bash
echo "no" | sudo tee /etc/pure-ftpd/conf/PAMAuthentication
```

### 3.3.2 创建PureDB认证链接
```bash  
sudo ln -sf /etc/pure-ftpd/conf/PureDB /etc/pure-ftpd/auth/50puredb
```

### 3.3.3 重启服务
```bash
sudo systemctl restart pure-ftpd
```

## 3.4 解决验证
```bash
timeout 10 ftp -nv *********** <<EOF
user hyqm_prod 8JpG5hzHHT8ExsDd
pwd
quit
EOF
```

**结果**: 
```
331 User hyqm_prod OK. Password required
230 OK. Current directory is /
Remote directory: /
```

**✅ 问题解决**: FTP登录认证成功

# 4 后续问题：VSCode插件连接超时

## 4.1 新问题描述
**时间**: 2025-07-31 14:10-14:23  
**现象**: VSCode SFTP插件一直在转圈，请求超时
**状态**: 登录认证已成功，但插件无法正常工作

## 4.2 日志分析
```bash
sudo journalctl -u pure-ftpd -n 20 --no-pager
```

### 4.2.1 关键信息
```
Jul 31 14:10:18 VM-0-17-ubuntu pure-ftpd[1779310]: (?@**********) [INFO] hyqm_prod is now logged in ✓
Jul 31 14:10:29 VM-0-17-ubuntu pure-ftpd[1779310]: (hyqm_prod@**********) [INFO] Logout.
Jul 31 14:21:39 VM-0-17-ubuntu pure-ftpd[1771548]: (hyqm_prod@**********) [INFO] Timeout - try typing a little faster next time
```

### 4.2.2 问题分析
- **登录成功**: `hyqm_prod is now logged in` ✓
- **频繁断开**: 多次 `Logout` 记录
- **超时问题**: `Timeout - try typing a little faster next time`
- **客户端IP**: ********** (OpenVPN分配的IP)

## 4.3 可能原因
1. **被动模式端口问题**: 端口21100-21200可能被阻塞
2. **网络延迟**: OpenVPN连接可能存在延迟导致超时
3. **VSCode插件配置**: 缺少被动模式配置
4. **防火墙**: 被动模式端口未开放

## 4.4 解决尝试1：启用被动模式
### 4.4.1 配置修改
在 `.vscode/sftp.json` 中添加：
```json
{
    "passive": true
}
```

### 4.4.2 新错误
```
[***********]: All configured authentication methods failed
```

### 4.4.3 问题分析
- **现象**: 添加被动模式后出现认证失败
- **可能原因**: VSCode SFTP插件的被动模式可能影响了认证流程
- **插件行为**: 可能将FTP协议误认为SFTP协议处理

# 5 待解决问题
- **问题性质**: 网络连接和被动模式配置问题
- **根本原因**: VSCode插件无法建立数据连接
- **状态**: 需要进一步排查被动模式端口和网络配置
# 前端排查记录：
[[2025-07-31 前端排查sftp连接问题]]