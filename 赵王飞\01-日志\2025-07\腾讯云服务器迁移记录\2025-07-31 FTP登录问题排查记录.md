---
date_created: 2025-07-31 13:49:01
date_modified: 2025-07-31 13:57:30
---
# 1 问题描述
[[1Panel FTP使用指南]]
## 1.1 错误信息
**时间**: 2025-07-31 13:37:20  
**错误类型**: Login authentication failed  
**插件**: VSCode SFTP插件 (Natizyskunk.sftp-1.16.3)

```
[07-31 13:37:20] [error] Error: Login authentication failed
    at g (c:\Users\<USER>\.cursor\extensions\natizyskunk.sftp-1.16.3\dist\extension.js:2:28508)
    at p.<anonymous> (c:\Users\<USER>\.cursor\extensions\natizyskunk.sftp-1.16.3\dist\extension.js:2:29232)
    at p.emit (node:events:524:28)
    at p._write (c:\Users\<USER>\.cursor\extensions\natizyskunk.sftp-1.16.3\dist\extension.js:2:42069)
    ...
```

## 1.2 配置信息
- **服务器**: ***********:21
- **用户名**: hyqm_prod
- **密码**: 8JpG5hzHHT8ExsDd
- **目标路径**: /opt/1panel/www/sites/hyqm2.nmzyb.cn/index

# 2 排查过程

## 2.1 权限检查
### 2.1.1 命令执行
```bash
# 检查目标目录权限
ls -la /opt/1panel/www/sites/hyqm2.nmzyb.cn/index

# 修改目录权限
chmod 755 /opt/1panel/www/sites/hyqm2.nmzyb.cn/index

# 检查上级目录权限
ls -la /opt/1panel/www/sites/hyqm2.nmzyb.cn
```

### 2.1.2 结果分析
- **目标目录**: `/opt/1panel/www/sites/hyqm2.nmzyb.cn/index`
  - 权限: `drwxr-xr-x` (755)
  - 所有者: ubuntu:ubuntu
  - 文件正常，权限充足

- **上级目录**: `/opt/1panel/www/sites/hyqm2.nmzyb.cn`
  - 权限: `drwxr-xr-x` (755)  
  - 所有者: root:root
  - **潜在问题**: 上级目录属于root，可能影响访问

## 2.2 用户状态验证
### 2.2.1 检查结果
- **1Panel用户状态**: hyqm_prod用户状态正常 ✓
- **用户名密码**: 确认无误 ✓  
- **网络连通性**: `telnet *********** 21` 可以正常访问 ✓

## 2.3 初步分析
- 目录权限正常
- 用户账户状态正常  
- 基础网络连通性正常
- **问题定位**: 不是目录权限、用户状态或基础连接问题，可能是**FTP协议层面的认证问题**或**VSCode插件配置问题**。

## 2.4 命令行FTP测试
### 2.4.1 测试命令
```bash
ftp ***********
```

### 2.4.2 测试结果
```
连接到 ***********。
220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------
220-You are user number 1 of 50 allowed.
220-Local time is now 13:55. Server port: 21.
220-This is a private system - No anonymous login
220-IPv6 connections are also welcome on this server.
220 You will be disconnected after 15 minutes of inactivity.
用户(***********:(none)): hyqm_prod
331 User hyqm_prod OK. Password required
530 Login authentication failed
```

### 2.4.3 关键发现
- **FTP服务**: Pure-FTPd [privsep] [TLS]
- **用户名识别**: 服务器接受用户名 hyqm_prod ✓
- **认证失败**: 即使用命令行也出现 `530 Login authentication failed` ❌
- **结论**: **问题确定在服务端**，不是VSCode插件问题

# 3 问题确认与解决

## 3.1 使用腾讯云MCP排查
**时间**: 2025-07-31 14:03-14:05

### 3.1.1 服务状态检查
```bash
ps aux | grep ftp
# 发现 Pure-FTPd 服务正常运行
root 1395535 0.0 0.0 12740 1876 ? Ss 11:43 0:00 pure-ftpd (SERVER)
```

### 3.1.2 用户数据库验证
```bash
pure-pw list
# 用户存在于数据库中
hyqm_prod	/opt/1panel/www/sites/hyqm2.nmzyb.cn/index/./
```

### 3.1.3 日志分析发现关键问题
```bash
sudo journalctl -u pure-ftpd -n 20 --no-pager
```

**关键发现**:
```
Jul 31 13:55:30 VM-0-17-ubuntu pure-ftpd[1731826]: pam_unix(pure-ftpd:auth): check pass; user unknown
Jul 31 13:55:30 VM-0-17-ubuntu pure-ftpd[1731826]: pam_unix(pure-ftpd:auth): authentication failure
```

## 3.2 问题根因确认
### 3.2.1 认证配置检查
```bash
cat /etc/pure-ftpd/conf/PAMAuthentication
# 结果: yes ❌

cat /etc/pure-ftpd/conf/PureDB  
# 结果: /etc/pure-ftpd/pureftpd.pdb ✓

ls -la /etc/pure-ftpd/auth/
# 缺少: 50puredb 链接 ❌
```

**问题根因**: 
1. PAM认证启用，优先于PureDB认证
2. 缺少PureDB认证模块链接

## 3.3 解决方案执行
### 3.3.1 禁用PAM认证
```bash
echo "no" | sudo tee /etc/pure-ftpd/conf/PAMAuthentication
```

### 3.3.2 创建PureDB认证链接
```bash  
sudo ln -sf /etc/pure-ftpd/conf/PureDB /etc/pure-ftpd/auth/50puredb
```

### 3.3.3 重启服务
```bash
sudo systemctl restart pure-ftpd
```

## 3.4 解决验证
```bash
timeout 10 ftp -nv *********** <<EOF
user hyqm_prod 8JpG5hzHHT8ExsDd
pwd
quit
EOF
```

**结果**: 
```
331 User hyqm_prod OK. Password required
230 OK. Current directory is /
Remote directory: /
```

**✅ 问题解决**: FTP登录认证成功

# 4 后续问题：VSCode插件连接超时

## 4.1 新问题描述
**时间**: 2025-07-31 14:10-14:23  
**现象**: VSCode SFTP插件一直在转圈，请求超时
**状态**: 登录认证已成功，但插件无法正常工作

## 4.2 日志分析
```bash
sudo journalctl -u pure-ftpd -n 20 --no-pager
```

### 4.2.1 关键信息
```
Jul 31 14:10:18 VM-0-17-ubuntu pure-ftpd[1779310]: (?@**********) [INFO] hyqm_prod is now logged in ✓
Jul 31 14:10:29 VM-0-17-ubuntu pure-ftpd[1779310]: (hyqm_prod@**********) [INFO] Logout.
Jul 31 14:21:39 VM-0-17-ubuntu pure-ftpd[1771548]: (hyqm_prod@**********) [INFO] Timeout - try typing a little faster next time
```

### 4.2.2 问题分析
- **登录成功**: `hyqm_prod is now logged in` ✓
- **频繁断开**: 多次 `Logout` 记录
- **超时问题**: `Timeout - try typing a little faster next time`
- **客户端IP**: ********** (OpenVPN分配的IP)

## 4.3 可能原因
1. **被动模式端口问题**: 端口21100-21200可能被阻塞
2. **网络延迟**: OpenVPN连接可能存在延迟导致超时
3. **VSCode插件配置**: 缺少被动模式配置
4. **防火墙**: 被动模式端口未开放

## 4.4 解决尝试1：启用被动模式
### 4.4.1 配置修改
在 `.vscode/sftp.json` 中添加：
```json
{
    "passive": true
}
```

### 4.4.2 新错误
```
[***********]: All configured authentication methods failed
```

### 4.4.3 问题分析
- **现象**: 添加被动模式后出现认证失败
- **可能原因**: VSCode SFTP插件的被动模式可能影响了认证流程
- **插件行为**: 可能将FTP协议误认为SFTP协议处理

# 5 问题二：被动模式数据连接超时

## 5.1 问题描述
**时间**: 2025-07-31 15:01-16:06
**现象**: 在问题一修复后，VSCode插件上传请求一直在转圈，出现被动模式连接超时

### 5.1.1 错误信息
```
Error: Can't open data connection in passive mode: connect ETIMEDOUT ***********:7971
Error: Can't open data connection in passive mode: connect ETIMEDOUT ***********:23908
```

## 5.2 根因分析

### 5.2.1 服务器端FTP测试
**测试结果**: 服务器内部FTP命令测试成功
- 主动模式: ✅ 成功，使用Extended Passive mode端口2578、63625
- 被动模式: ✅ 成功，使用端口39233、50381
- PASV命令: ✅ 成功，使用端口12020

### 5.2.2 本机FTP测试 - 关键发现
**测试命令**: 本机FTP连接***********
**关键错误**:
```
I won't open a connection to ************ (only to **********)
```

**网络环境分析**:
- 客户端IP: ********** (OpenVPN分配)
- 服务器尝试连接: ************ (另一个网络接口)
- 服务器目标: *********** (Docker网络)

### 5.2.3 问题根因确认
**OpenVPN转发机制限制**:
1. **主动模式问题**: 服务器需要反向连接客户端，但OpenVPN只允许客户端发起连接
2. **被动模式问题**: 服务器使用随机端口(7971, 23908等)，这些端口未在防火墙中开放
3. **网络路由问题**: 多网络接口导致连接到错误的IP地址

## 5.3 解决方案实施

### 5.3.1 防火墙配置检查
```bash
sudo ufw status
# 发现已配置: 39000:40000/tcp ALLOW Anywhere
```

### 5.3.2 Pure-FTPd被动模式配置
**问题**: 防火墙已开放39000-40000端口，但Pure-FTPd未配置使用此范围

**解决步骤**:
```bash
# 1. 配置被动模式端口范围
echo "39000 40000" | sudo tee /etc/pure-ftpd/conf/PassivePortRange

# 2. 重启服务
sudo systemctl restart pure-ftpd

# 3. 测试验证
timeout 30 ftp -nv *********** <<EOF
user hyqm_prod 8JpG5hzHHT8ExsDd
quote PASV
put /tmp/ftp_test.txt test_new_passive.txt
quit
EOF
```

**测试结果**:
```
227 Entering Passive Mode (172,17,0,17,155,6)
229 Extended Passive mode OK (|||39317|)  ← 使用配置范围内端口
150 Accepted data connection
226-File successfully transferred
```

### 5.3.3 安全优化
**缩小端口范围** (从1000个端口缩减到11个):
```bash
# 1. 更新Pure-FTPd配置
echo "39000 39010" | sudo tee /etc/pure-ftpd/conf/PassivePortRange

# 2. 更新防火墙规则
sudo ufw delete allow 39000:40000/tcp
sudo ufw allow 39000:39010/tcp

# 3. 重启服务
sudo systemctl restart pure-ftpd
```

**最终测试**:
```
229 Extended Passive mode OK (|||39010|)  ← 使用端口39010
150 Accepted data connection
226-File successfully transferred
```

## 5.4 解决方案总结

### 5.4.1 最终配置
- **被动模式端口范围**: 39000-39010 (11个端口)
- **防火墙规则**: 39000:39010/tcp ALLOW
- **安全改进**: 攻击面减少99% (从1000个端口到11个)

### 5.4.2 技术原理
**被动模式解决OpenVPN问题**:
- 所有连接都由客户端发起，避免服务器反向连接
- 使用固定端口范围，确保防火墙正确开放
- 符合OpenVPN的单向转发机制

### 5.4.3 预期效果
- ✅ 解决VSCode插件连接超时问题
- ✅ 避免OpenVPN反向连接限制
- ✅ 提供安全的FTP数据传输通道

**状态**: ✅ **问题已完全解决**

## 5.5 VSCode插件测试验证

### 5.5.1 测试结果
**时间**: 2025-07-31 16:06
**测试状态**: ✅ **成功**
**现象**: VSCode插件上传文件不再出现连接超时，正常工作

### 5.5.2 解决方案验证
- ✅ **被动模式配置生效**: 使用39000-39010端口范围
- ✅ **防火墙规则正确**: 端口正常开放和访问
- ✅ **OpenVPN兼容性**: 避免了反向连接问题
- ✅ **安全性提升**: 攻击面减少99%

### 5.5.3 最终状态
**问题**: FTP登录认证失败 + 被动模式数据连接超时
**解决方案**: PAM认证禁用 + PureDB认证启用 + 被动模式端口范围配置
**结果**: ✅ **VSCode FTP插件完全正常工作**

# 6 问题解决总结

## 6.1 问题回顾
1. **问题一**: FTP登录认证失败 (530 Login authentication failed)
2. **问题二**: 被动模式数据连接超时 (connect ETIMEDOUT)

## 6.2 解决方案汇总

### 6.2.1 问题一解决方案
```bash
# 禁用PAM认证
echo "no" | sudo tee /etc/pure-ftpd/conf/PAMAuthentication

# 创建PureDB认证链接
sudo ln -sf /etc/pure-ftpd/conf/PureDB /etc/pure-ftpd/auth/50puredb

# 重启服务
sudo systemctl restart pure-ftpd
```

### 6.2.2 问题二解决方案
```bash
# 配置被动模式端口范围
echo "39000 39010" | sudo tee /etc/pure-ftpd/conf/PassivePortRange

# 配置防火墙规则
sudo ufw allow 39000:39010/tcp

# 重启服务
sudo systemctl restart pure-ftpd
```

## 6.3 最终配置状态
- **FTP认证方式**: PureDB认证 (PAM认证已禁用)
- **被动模式端口**: 39000-39010 (11个端口)
- **防火墙规则**: 39000:39010/tcp开放
- **服务状态**: Pure-FTPd正常运行
- **VSCode插件**: ✅ 完全正常工作

## 6.4 技术要点总结
1. **认证问题**: PAM认证与PureDB认证冲突，需禁用PAM
2. **网络问题**: OpenVPN环境下主动模式失败，需使用被动模式
3. **端口问题**: 随机端口被防火墙阻止，需配置固定端口范围
4. **安全考虑**: 最小化端口开放范围，减少攻击面

## 6.5 经验教训
- **网络环境复杂性**: VPN、Docker、多网卡环境需特别注意FTP模式选择
- **防火墙配置**: 被动模式需要开放数据端口范围
- **服务配置一致性**: 防火墙开放的端口必须与FTP服务配置一致
- **安全与功能平衡**: 在满足功能需求的前提下最小化安全风险

**✅ 问题完全解决，VSCode FTP插件正常工作**

---

# 前端排查记录：
[[2025-07-31 前端排查sftp连接问题]]