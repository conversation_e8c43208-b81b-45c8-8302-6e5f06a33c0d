# 汇远轻媒2.0系统运维手册

## 项目概览

**系统名称**: 汇远轻媒2.0系统  
**访问域名**: hyqm2.nmzyb.cn  
**技术架构**: Django + MySQL + Redis + RabbitMQ  
**服务器环境**: Ubuntu 24.04 + Docker容器化部署  
**数据库**: hyqm2 - Django Web应用

## 服务架构

```
外部访问 → 雷池WAF → 1Panel OpenResty → {
    ├── 静态文件请求 → 直接返回 (/www/sites/hyqm2.nmzyb.cn/index/)
    └── API请求 (/api/*) → 反向代理 → hyqm2-prod:9006
}
```

### 核心容器服务

| 容器名称 | 镜像 | 端口映射 | 用途 |
|---------|------|----------|------|
| hyqm2-prod | zhao0829wang/hyqm-django:3.12.7-v2 | 9006:8000 | 汇远轻媒2.0主应用 |
| mysql-prod | mysql:5.7.44 | 3306 | 数据库服务 |
| redis-prod | redis:8.0.3 | 6379 | 缓存服务 |
| rabbitmq-prod | rabbitmq:4.1.2-management-alpine | 5672:5672 | 消息队列服务 |

## 目录结构

```
/mnt/datadisk0/
├── volumns/hyqm2/hyqm2-prod/            # 轻媒2.0应用数据目录
│   └── django-vue3-admin/backend/       # Django后端代码
│       ├── logs/                        # 应用日志
│       ├── env_tencent_prod.py          # 生产环境配置
│       └── supervisord-prod.conf        # Supervisord配置
└── /opt/1panel/www/sites/hyqm2.nmzyb.cn/
    ├── index/                           # 前端静态文件目录
    │   ├── index.html
    │   ├── assets/
    │   └── js/
    ├── proxy/                           # nginx配置文件目录
    │   ├── root.conf                    # API反向代理配置
    │   └── static.conf                  # 静态文件服务配置
    └── log/                             # 访问日志
```

## 数据库信息

### 主要数据库
- **数据库名**: hyqm2
- **业务**: 汇远轻媒2.0
- **技术栈**: Django框架
- **用途**: 轻媒2.0内容管理和API服务

### 数据库连接信息
- **主机**: mysql-prod (容器内网络)
- **用户**: hyqm_prod
- **密码**: zbQBVTwNUT#j@T*n
- **端口**: 3306

## 服务状态检查

```bash
# 检查轻媒2.0容器状态
docker ps | grep "hyqm2-prod"

# 检查容器资源使用情况
docker stats --no-stream hyqm2-prod

# 检查应用端口
netstat -tlnp | grep 9006

# 检查进程状态
docker exec hyqm2-prod ps aux | grep python

# 检查Supervisord状态
docker exec hyqm2-prod supervisorctl status

# 检查Django应用状态
docker exec hyqm2-prod curl -I http://localhost:8000/

# 检查容器健康状态
docker inspect hyqm2-prod | grep -A 5 "Health"
```

## 日志查看

### Django应用日志
```bash
# 轻媒2.0应用容器日志
docker logs hyqm2-prod --tail 100 -f

# 实时查看最新日志
docker logs hyqm2-prod --tail 50 --follow

# 查看特定时间段日志
docker logs hyqm2-prod --since="2h" --until="1h"

# Django应用日志文件
docker exec hyqm2-prod tail -f /backend/logs/django.out.log

# Django错误日志
docker exec hyqm2-prod tail -f /backend/logs/django.err.log

# Supervisord日志
docker exec hyqm2-prod tail -f /var/log/supervisor/supervisord.log

# 查看日志文件大小
docker exec hyqm2-prod du -sh /backend/logs/*

# 搜索特定关键词日志
docker exec hyqm2-prod grep -n "ERROR" /backend/logs/django.out.log | tail -20
docker exec hyqm2-prod grep -n "Exception" /backend/logs/django.err.log | tail -10
```

### 1Panel网站访问日志
```bash
# 轻媒2.0访问日志
tail -f /opt/1panel/www/sites/hyqm2.nmzyb.cn/log/access.log

# 错误日志
tail -f /opt/1panel/www/sites/hyqm2.nmzyb.cn/log/error.log

# 统计今日访问量
grep "$(date '+%d/%b/%Y')" /opt/1panel/www/sites/hyqm2.nmzyb.cn/log/access.log | wc -l

# 查看最近访问记录
tail -20 /opt/1panel/www/sites/hyqm2.nmzyb.cn/log/access.log

# 分析访问IP统计
awk '{print $1}' /opt/1panel/www/sites/hyqm2.nmzyb.cn/log/access.log | sort | uniq -c | sort -nr | head -10

# 查看API请求统计
grep "/api/" /opt/1panel/www/sites/hyqm2.nmzyb.cn/log/access.log | wc -l

# 查看静态文件请求统计
grep -E "\.(js|css|png|jpg|ico)$" /opt/1panel/www/sites/hyqm2.nmzyb.cn/log/access.log | wc -l

# 实时监控访问日志（区分API和静态文件）
tail -f /opt/1panel/www/sites/hyqm2.nmzyb.cn/log/access.log | grep --color=always -E "(GET|POST|PUT|DELETE)"
```

### 系统日志监控
```bash
# 查看系统级容器日志
journalctl -u docker -f | grep hyqm2-prod

# 监控容器资源使用实时变化
watch -n 2 'docker stats --no-stream hyqm2-prod'

# 查看网络连接状态
docker exec hyqm2-prod netstat -tuln | grep :8000

# 检查Django数据库连接
docker exec hyqm2-prod python /backend/manage.py check --database default
```

## 业务特定日志

### Django应用日志
```bash
# 查看Django请求日志
docker exec hyqm2-prod grep -n "request" /backend/logs/django.out.log | tail -20

# 查看数据库查询日志
docker exec hyqm2-prod grep -n "sql" /backend/logs/django.out.log | tail -20

# 查看认证相关日志
docker exec hyqm2-prod grep -n "auth" /backend/logs/django.out.log | tail -20

# 查看API访问日志
docker exec hyqm2-prod grep -n "api" /backend/logs/django.out.log | tail -20
```

### Supervisord进程管理日志
```bash
# 查看所有监控进程状态
docker exec hyqm2-prod supervisorctl status all

# 查看Django进程启动日志
docker exec hyqm2-prod supervisorctl tail django

# 查看特定进程的错误日志
docker exec hyqm2-prod supervisorctl tail django stderr

# 重启Django进程
docker exec hyqm2-prod supervisorctl restart django

# 查看Supervisord配置
docker exec hyqm2-prod cat /etc/supervisor/supervisord.conf
```

### 前端静态文件访问监控
```bash
# 检查静态文件目录
ls -la /opt/1panel/www/sites/hyqm2.nmzyb.cn/index/

# 查看静态文件配置
cat /opt/1panel/www/sites/hyqm2.nmzyb.cn/proxy/static.conf

# 测试静态文件访问
curl -I http://hyqm2.nmzyb.cn/
curl -I http://hyqm2.nmzyb.cn/favicon.ico

# 测试API代理
curl -I http://hyqm2.nmzyb.cn/api/
```

## 日志轮转和清理

```bash
# 查看日志文件大小统计
docker exec hyqm2-prod find /backend/logs -name "*.log" -exec ls -lh {} \; | sort -k5 -hr

# 压缩旧日志文件
docker exec hyqm2-prod find /backend/logs -name "*.log.*" -mtime +7 -exec gzip {} \;

# 清理过旧的压缩日志
docker exec hyqm2-prod find /backend/logs -name "*.gz" -mtime +30 -delete

# 检查日志目录使用情况
docker exec hyqm2-prod du -sh /backend/logs

# 清理1Panel访问日志（保留最近30天）
find /opt/1panel/www/sites/hyqm2.nmzyb.cn/log -name "*.log.*" -mtime +30 -delete
```

## 环境配置

### 生产环境配置文件
```python
# /mnt/datadisk0/volumns/hyqm2/hyqm2-prod/django-vue3-admin/backend/env_tencent_prod.py

# 数据库配置
DATABASE_HOST = 'mysql-prod'
DATABASE_PORT = 3306
DATABASE_USER = "hyqm_prod"
DATABASE_PASSWORD = 'zbQBVTwNUT#j@T*n'
DATABASE_NAME = "hyqm2"

# Redis 配置
REDIS_HOST = "redis-prod"
REDIS_PORT = 6379

# RabbitMQ 配置
RABBITMQ_HOST = 'rabbitmq-prod'
RABBITMQ_URL = 'amqp://hyxx:hyxx123@rabbitmq-prod:5673'
```

### 容器启动配置
```bash
# 生产环境容器启动命令
sudo docker run -d \
--restart=always \
--name hyqm2-prod \
--network 1panel-network \
-p 9006:8000 \
-e TZ=Asia/Shanghai \
-e DJANGO_ENV=tencent_prod \
-v /mnt/datadisk0/volumns/hyqm2/hyqm2-prod/django-vue3-admin/backend:/backend \
-w /backend \
-v /mnt/datadisk0/volumns/hyqm2/hyqm2-prod/django-vue3-admin/backend/supervisord-prod.conf:/etc/supervisor/supervisord.conf \
zhao0829wang/hyqm-django:3.12.7-v2 supervisord
```

## 更新记录

| 日期 | 版本 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 2025-07-30 | v1.0 | 汇远轻媒2.0系统精简运维手册 | Claude |

---
**维护负责人**: 运维团队  
**紧急联系**: 技术支持部门  
**业务联系人**: 汇远轻媒产品团队