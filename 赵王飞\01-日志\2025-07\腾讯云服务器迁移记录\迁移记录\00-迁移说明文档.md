# 迁移任务概述、核心信息与标准化流程

### 1. 核心架构与原则

新的腾讯云服务器采用以 Docker 为核心、1Panel 为管理面板的现代化架构。所有业务都将运行在容器中，以实现环境隔离和标准化部署。

#### 架构图 (Mermaid)
```mermaid
graph TD
    subgraph "外部访问"
        A[用户 / Internet]
    end

    subgraph "腾讯云服务器 (Docker Host)"
        B(入口 / 防火墙 / 1Panel OpenResty)
        subgraph "Docker 环境 (虚拟网络: 1panel-network)"
            D[apache_php_service]
            E[mysql-prod]
            F[redis-prod]
            G[其他业务容器...]
        end
    end

    A --> B
    B --> D

    D -- "通过容器名 'mysql-prod' 连接" --> E
    D -- "通过容器名 'redis-prod' 连接" --> F
```

#### 关键原则与模式转变
1.  **容器化优先**: 所有服务都必须在 Docker 容器中运行。
2.  **统一网络**: 所有需要相互通信的容器**必须**连接到 `1panel-network` 网络。
3.  **配置模式转变**: 配置文件中必须使用**容器服务名** (如 `mysql-prod`) 代替旧的 **IP 地址**进行服务连接。

---
### 2. 新服务器关键服务容器列表

以下是新服务器上已部署并正在运行的核心服务容器：

| 容器名称             | 镜像                               | 作用说明             |
| :------------------- | :--------------------------------- | :------------------- |
| `mysql-prod`         | `mysql:5.7.44`                     | **生产环境**主数据库 |
| `redis-prod`         | `redis:8.0.3`                      | **生产环境**主缓存   |
| `apache_php_service` | `zhao0829wang/apache2-php:7.2-rc4` | 本次迁移的PHP服务    |
| `1Panel-openresty-YFc6`| `1panel/openresty:********-2-1-focal` | 1Panel主网关/反代  |
| `1Panel-memcached-ASDM`| `memcached:1.6.22-alpine3.18`      | Memcached缓存服务    |
| `icbc-naoer-web`     | `zhao0829wang/apache2-php:7.2-rc4` | 其他已迁移的PHP服务  |
| `icbc-chifeng-web`   | `zhao0829wang/apache2-php:7.2-rc4` | 其他已迁移的PHP服务  |

---
### 3. 标准化传输与迁移流程

#### 3.1 文件传输总则 (HTTP 直传)
- **原则**: 为实现两台服务器之间快速、高效的文件传输，我们采用“HTTP直传”模式。
- **指定中转平台**: 旧环境中的 `************` 服务器因其对外开放了Web服务（域名 `main.51zsqc.com`），故被指定为唯一的文件中转平台。
- **流程**:
    1.  所有备份和打包操作均在**中转平台** (`************`) 上执行。对于数据库等远程资源，将在 `208` 上执行远程命令进行备份，文件直接生成在 `208` 的文件系统上，然后放置到 Web 目录 `/usr/share/nginx/html/transfer/` 中。
    2.  在**目标服务器**（腾讯云）上，**必须**使用域名 `main.51zsqc.com` 通过 `wget` 下载文件。
    3.  **安全措施**: 文件下载完成后，应**立即**从中转平台的Web目录中删除该临时文件。

#### 3.2 应用场景 1: 迁移项目文件
1.  **打包 (旧服务器)**: `tar -czf [项目名].tar.gz [项目目录]`
2.  **提供下载 (中转平台 `208`)**: `mv [项目名].tar.gz /usr/share/nginx/html/transfer/`
3.  **下载 (新服务器)**: `wget https://main.51zsqc.com/transfer/[项目名].tar.gz`
4.  **清理 (中转平台 `208`)**: `rm /usr/share/nginx/html/transfer/[项目名].tar.gz`
5.  **解压 (新服务器)**: `tar -xzf [项目名].tar.gz -C /mnt/datadisk0/volumns/php_home_208/`

#### 3.3 应用场景 2: 迁移数据库
1.  **备份 (从`208`远程执行)**: `mysqldump -h ************ ... > ...`
2.  **压缩并提供下载 (中转平台 `208`)**: `tar -czf /usr/share/nginx/html/transfer/[库名].sql.tar.gz ...`
3.  **下载 (新服务器)**: `wget https://main.51zsqc.com/transfer/[库名].sql.tar.gz`
4.  **清理 (中转平台 `208`)**: `rm /usr/share/nginx/html/transfer/[库名].sql.tar.gz`
5.  **导入 (新服务器)**:
    - 解压: `tar -xzf [库名].sql.tar.gz`
    - 复制到容器: `docker cp [库名].sql mysql-prod:/tmp/`
    - 执行导入: `docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' [库名] < /tmp/[库名].sql`
6.  **最终清理**: 删除新服务器上及容器内的所有临时文件。


---
### 5. 核心环境凭证信息

| 分类        | 项目           | 值                                                                            |
| :-------- | :----------- | :--------------------------------------------------------------------------- |
| **旧环境集群** | 服务器IP列表      | `************` (操作平台), `************`, `************`, `************` (数据库源) |
|           | 数据库源 Root 密码 | `TxkjDB2020#`                                                                |
| **新服务器**  | 数据库容器名       | `mysql-prod`                                                                 |
|           | 数据库 Root 密码  | `56d9DavJ*zwrwj9rmA`                                                         |
|           | 业务数据库用户      | `pearproject`                                                                |
|           | 业务数据库密码      | `kG7#tPq9@zR2$vX1`                                                           |
