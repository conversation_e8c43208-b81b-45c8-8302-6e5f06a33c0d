---
date_created: 2025-07-25 18:15:29
date_modified: 2025-07-25 18:15:30
author: 赵王飞
---

# 背景 

配置雷池HTTPS证书时提交后报错：
```
请求网址: https://111.229.155.148:9443/api/open/cert
请求方法: POST
状态代码: 500 Internal Server Error
错误信息: "dial tcp: lookup acme-v02.api.letsencrypt.org on 127.0.0.53:53: read: connection refused"
```

# 问题根因

在之前解决容器间相互访问问题时（参见`2025-07-21 容器之间相互访问的方案.md`），错误地创建了一个名为`dns-bridge`的容器，该容器占用了53端口，导致系统原生的DNS解析服务（systemd-resolved）无法正常工作。

雷池是通过Docker容器部署的，容器内部也受到了DNS解析失败的影响。

# 解决过程

## 1. 检查并删除问题容器
```bash
# 查找占用53端口的容器
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "53|dns|DNS"
# 输出：dns-bridge    Up 2 days    53/tcp, 53/udp

# 停止并删除dns-bridge容器
docker stop dns-bridge && docker rm dns-bridge

# 删除相关网络
docker network disconnect dns-network mysql-test
docker network rm dns-network
```

## 2. 恢复系统DNS服务
```bash
# 重启systemd-resolved
sudo systemctl restart systemd-resolved

# 验证系统DNS解析
nslookup acme-v02.api.letsencrypt.org
# 成功返回解析结果
```

## 3. 修复Docker容器DNS解析
```bash
# 检查雷池容器DNS配置
docker exec safeline-mgt cat /etc/resolv.conf
# 发现使用Docker内部DNS（127.0.0.11）

# 测试容器内DNS解析失败
docker exec safeline-mgt ping -c 1 acme-v02.api.letsencrypt.org
# 输出：ping: bad address

# 重启Docker服务以刷新DNS链
sudo systemctl restart docker

# 重启雷池管理容器
docker restart safeline-mgt

# 再次测试容器内DNS解析
docker exec safeline-mgt ping -c 1 acme-v02.api.letsencrypt.org
# 成功：64 bytes from *************

# 验证可以访问Let's Encrypt API
docker exec safeline-mgt wget -O- -T 5 https://acme-v02.api.letsencrypt.org/directory
# 成功返回API响应
```

# 解决结果

1. **系统层面**：删除了错误创建的dns-bridge容器，恢复了systemd-resolved服务
2. **Docker层面**：通过重启Docker服务和容器，修复了容器内的DNS解析链
3. **最终验证**：雷池容器现在可以正常解析并访问Let's Encrypt的API服务器，证书申请功能已恢复正常