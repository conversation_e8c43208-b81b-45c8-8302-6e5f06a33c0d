# Java容器内存优化标准化文档

## 文档说明

**创建时间：** 2025-07-25  
**版本：** v1.0  
**适用范围：** 基于Docker的Java Spring Boot应用容器  
**实践基础：** 腾讯云服务器容器内存优化实战经验

## 目录

1. [概述](#概述)
2. [问题识别](#问题识别)
3. [优化策略](#优化策略)
4. [配置模板](#配置模板)
5. [实施流程](#实施流程)
6. [验证方法](#验证方法)
7. [监控维护](#监控维护)
8. [故障排除](#故障排除)

## 概述

### 优化目标
- 减少Java容器内存占用60%-70%
- 控制JVM堆内存在合理范围
- 提升服务器资源利用率
- 保持应用性能和稳定性

### 适用场景
- Spring Boot应用容器
- yudao框架和ruoyi框架
- 大内存服务器(16GB+)部署的Java应用
- 无JVM内存参数限制的默认部署

### 关键收益
- **内存节省：** 单容器可节省1-3GB内存
- **成本降低：** 提高服务器资源利用率
- **性能提升：** 减少GC压力，降低响应延迟
- **稳定性：** 避免内存泄露风险

## 问题识别

### 典型症状

#### 1. 内存使用异常高
```bash
# 检查容器内存使用
docker stats --no-stream

# 异常指标：
# - 单个Java应用容器 > 2GB
# - yudao框架容器 > 1.5GB  
# - ruoyi框架容器 > 1GB
```

#### 2. JVM内存分配过大
```bash
# 检查Java进程内存
cat /proc/{PID}/status | grep -E "(VmRSS|VmSize|VmPeak)"

# 异常指标：
# - VmSize > 10GB (虚拟内存过大)
# - 无-Xmx参数限制
# - 默认使用系统内存1/4作为堆大小
```

#### 3. 应用特征分析
```bash
# 检查加载类数量
curl -s "http://localhost:PORT/actuator/metrics/jvm.classes.loaded"

# 检查堆内存使用
curl -s "http://localhost:PORT/actuator/metrics/jvm.memory.used?tag=area:heap"

# 异常指标：
# - 类加载数 > 20000
# - 堆内存使用 > 2GB
# - 最大堆内存 > 6GB
```

### 根本原因
1. **JVM默认行为：** 大内存服务器上JVM默认分配过多内存
2. **缺少限制：** 容器启动时未设置-Xmx等内存参数
3. **框架特性：** Spring Boot应用默认加载较多组件和类

## 优化策略

### 核心原则
1. **合理限制：** 根据应用实际需求设置堆内存上限
2. **分层配置：** 不同框架和应用类型使用不同参数
3. **性能平衡：** 在内存节省和性能之间找到最佳平衡点
4. **渐进优化：** 先保守配置，再根据运行情况调整

### 内存配置策略

#### yudao框架应用
- **推荐配置：** `-Xmx1024m`
- **适用场景：** 企业级应用，多数据源连接
- **预期内存：** 800MB - 1.2GB

#### ruoyi框架应用  
- **推荐配置：** `-Xmx768m`
- **适用场景：** 中等复杂度应用
- **预期内存：** 500MB - 800MB

#### 轻量应用
- **推荐配置：** `-Xmx512m`
- **适用场景：** 简单微服务
- **预期内存：** 300MB - 600MB

## 配置模板

### yudao框架容器优化模板

```bash
# 停止现有容器
docker stop {CONTAINER_NAME}
docker rm {CONTAINER_NAME}

# 重新部署优化容器
docker run -d --name {CONTAINER_NAME} \
  --network {NETWORK_NAME} \
  -p {HOST_PORT}:80 \
  -v {WORKDIR_PATH}:/workdir:rw \
  -v {LOGS_PATH}:/home/<USER>/logs:rw \
  -v {UI_PATH}:/home/<USER>/projects/ruoyi-ui:rw \
  -v {NGINX_CONFIG}:/etc/nginx/nginx.conf:ro \
  -v /etc/localtime:/etc/localtime:ro \
  -e TZ=Asia/Shanghai \
  -e COMMAND_NAME="-Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -jar yudao-server.jar {APPLICATION_PARAMS}" \
  {IMAGE_NAME}
```

### ruoyi框架容器优化模板

```bash
# 停止现有容器
docker stop {CONTAINER_NAME}
docker rm {CONTAINER_NAME}

# 重新部署优化容器
docker run -d --name {CONTAINER_NAME} \
  --network {NETWORK_NAME} \
  -p {HOST_PORT}:80 \
  -v {WORKDIR_PATH}:/workdir:rw \
  -v {LOGS_PATH}:/home/<USER>/logs:rw \
  -v {UI_PATH}:/home/<USER>/projects/ruoyi-ui:rw \
  -v /etc/localtime:/etc/localtime:ro \
  -e TZ=Asia/Shanghai \
  -e COMMAND_NAME="-Xms256m -Xmx768m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -jar ruoyi-admin.jar {APPLICATION_PARAMS}" \
  {IMAGE_NAME}
```

### JVM参数详解

#### 基础内存参数
```bash
-Xms512m          # 初始堆大小，避免频繁扩容
-Xmx1024m         # 最大堆大小，核心限制参数
-XX:MetaspaceSize=128m      # 元空间初始大小
-XX:MaxMetaspaceSize=256m   # 元空间最大大小
```

#### 垃圾收集器优化
```bash
-XX:+UseG1GC      # 使用G1垃圾收集器，适合大内存应用
-XX:MaxGCPauseMillis=200    # 最大GC暂停时间200ms
-XX:G1HeapRegionSize=16m    # G1堆区域大小(可选)
```

#### 进阶优化参数(可选)
```bash
-XX:+UseCompressedOops      # 启用压缩指针
-XX:+UseCompressedClassPointers  # 启用压缩类指针
-XX:+UseLargePages          # 使用大页内存(需系统支持)
-Xmn256m                    # 年轻代大小(约为堆大小的1/4)
```

### 参数配置对照表

| 应用类型 | 框架 | -Xms | -Xmx | MetaspaceSize | MaxMetaspaceSize | 预期内存 |
|----------|------|------|------|---------------|------------------|-----------|
| 大型企业应用 | yudao | 512m | 1024m | 128m | 256m | 800MB-1.2GB |
| 中型应用 | ruoyi | 256m | 768m | 128m | 256m | 500MB-800MB |
| 小型微服务 | 通用 | 128m | 512m | 64m | 128m | 300MB-600MB |
| 开发环境 | 通用 | 128m | 256m | 64m | 128m | 200MB-400MB |

## 实施流程

### 准备阶段

#### 1. 环境检查
```bash
# 检查系统内存
free -h

# 检查容器运行状态
docker ps

# 检查容器内存使用
docker stats --no-stream
```

#### 2. 备份配置
```bash
# 备份容器配置
docker inspect {CONTAINER_NAME} > /tmp/{CONTAINER_NAME}-backup-config.json

# 备份应用数据(如有必要)
docker exec {CONTAINER_NAME} tar -czf /tmp/app-backup.tar.gz /workdir
```

#### 3. 记录基线
```bash
# 记录优化前状态
docker stats {CONTAINER_NAME} --no-stream > /tmp/{CONTAINER_NAME}-before-stats.txt

# 记录Java进程信息
docker exec {CONTAINER_NAME} curl -s "http://localhost:PORT/actuator/metrics/jvm.memory.used"
```

### 实施阶段

#### 1. 停止应用
```bash
# 优雅停止容器
docker stop {CONTAINER_NAME}

# 删除容器(保留数据卷)
docker rm {CONTAINER_NAME}
```

#### 2. 应用优化配置
```bash
# 使用对应的模板重新部署
# 根据框架类型选择yudao或ruoyi模板
```

#### 3. 启动验证
```bash
# 检查容器启动状态
docker ps | grep {CONTAINER_NAME}

# 检查应用日志
docker logs --tail 20 {CONTAINER_NAME}

# 等待应用完全启动(建议60-120秒)
sleep 60
```

### 验证阶段

#### 1. 功能测试
```bash
# 健康检查
curl -s http://localhost:PORT/actuator/health

# 主要功能页面访问测试
curl -s http://localhost:PORT/ | head -1

# 数据库连接测试
docker logs {CONTAINER_NAME} | grep -i "database\|mysql\|connection"
```

#### 2. 性能验证
```bash
# 检查内存使用
docker stats {CONTAINER_NAME} --no-stream

# 检查JVM内存状态
curl -s "http://localhost:PORT/actuator/metrics/jvm.memory.used?tag=area:heap"
curl -s "http://localhost:PORT/actuator/metrics/jvm.memory.max?tag=area:heap"

# 检查GC性能
curl -s "http://localhost:PORT/actuator/metrics/jvm.gc.pause"
```

## 验证方法

### 成功标准

#### 内存使用标准
- yudao框架：容器内存 < 1.5GB
- ruoyi框架：容器内存 < 1GB  
- 堆内存使用率：40%-80%
- 元空间使用 < 200MB

#### 性能标准
- 应用启动时间 < 2分钟
- API响应时间无明显增加
- GC暂停时间 < 300ms
- 无OutOfMemoryError异常

#### 功能标准
- 健康检查返回UP状态
- 主要业务功能正常
- 数据库连接正常
- 定时任务执行正常

### 监控指标

#### 关键监控点
```bash
# 容器级别监控
docker stats {CONTAINER_NAME} --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"

# JVM级别监控
curl -s "http://localhost:PORT/actuator/metrics/jvm.memory.used" | jq '.measurements[0].value'
curl -s "http://localhost:PORT/actuator/metrics/jvm.gc.memory.allocated" | jq '.measurements[0].value'
```

#### 异常预警阈值
- 容器内存使用率 > 80%
- 堆内存使用率 > 90%
- GC频率 > 每分钟10次
- Full GC持续时间 > 5秒

## 监控维护

### 自动化监控脚本

#### 容器内存监控脚本
```bash
#!/bin/bash
# 文件: monitor_containers.sh

# 配置
CONTAINERS=("hyrm-imppc" "hyrm-imusti" "ruoyi-crm" "ruoyi-hyrm")
MEMORY_THRESHOLD=80  # 内存使用率阈值(%)
LOG_FILE="/var/log/container_memory_monitor.log"

# 监控函数
monitor_container() {
    local container=$1
    local stats=$(docker stats $container --no-stream --format "{{.MemPerc}}")
    local mem_percent=$(echo $stats | sed 's/%//')
    
    if (( $(echo "$mem_percent > $MEMORY_THRESHOLD" | bc -l) )); then
        echo "$(date): WARNING - $container memory usage: $stats" >> $LOG_FILE
        # 发送告警 (可集成企业微信、钉钉等)
        # curl -X POST "webhook_url" -d "Container $container memory high: $stats"
    fi
    
    echo "$container: $stats"
}

# 主逻辑
echo "$(date): Starting container memory monitoring..." >> $LOG_FILE
for container in "${CONTAINERS[@]}"; do
    if docker ps | grep -q $container; then
        monitor_container $container
    else
        echo "$(date): ERROR - Container $container is not running" >> $LOG_FILE
    fi
done
```

#### JVM内存监控脚本
```bash
#!/bin/bash
# 文件: monitor_jvm_memory.sh

# 配置
declare -A CONTAINERS_PORTS=(
    ["hyrm-imppc"]="48080"
    ["hyrm-imusti"]="48085"
    ["ruoyi-crm"]="80"
    ["ruoyi-hyrm"]="80"
)

# 监控JVM内存
monitor_jvm() {
    local container=$1
    local port=$2
    
    # 获取堆内存使用情况
    local heap_used=$(docker exec $container curl -s "http://localhost:$port/actuator/metrics/jvm.memory.used?tag=area:heap" 2>/dev/null | jq -r '.measurements[0].value // "N/A"')
    local heap_max=$(docker exec $container curl -s "http://localhost:$port/actuator/metrics/jvm.memory.max?tag=area:heap" 2>/dev/null | jq -r '.measurements[0].value // "N/A"')
    
    if [[ "$heap_used" != "N/A" && "$heap_max" != "N/A" ]]; then
        local usage_percent=$(echo "scale=2; $heap_used / $heap_max * 100" | bc)
        echo "$container JVM Heap: ${usage_percent}% ($(echo "scale=0; $heap_used / 1048576" | bc)MB / $(echo "scale=0; $heap_max / 1048576" | bc)MB)"
        
        # 检查是否超过90%
        if (( $(echo "$usage_percent > 90" | bc -l) )); then
            echo "$(date): CRITICAL - $container JVM heap usage: ${usage_percent}%" >> /var/log/jvm_memory_monitor.log
        fi
    else
        echo "$container: JVM metrics unavailable"
    fi
}

# 执行监控
for container in "${!CONTAINERS_PORTS[@]}"; do
    if docker ps | grep -q $container; then
        monitor_jvm $container ${CONTAINERS_PORTS[$container]}
    fi
done
```

### 定时任务配置

#### Crontab设置
```bash
# 编辑定时任务
crontab -e

# 添加以下内容
# 每5分钟检查一次容器内存
*/5 * * * * /path/to/monitor_containers.sh

# 每10分钟检查一次JVM内存
*/10 * * * * /path/to/monitor_jvm_memory.sh

# 每天凌晨生成内存使用报告
0 1 * * * /path/to/generate_memory_report.sh
```

#### 内存使用报告脚本
```bash
#!/bin/bash
# 文件: generate_memory_report.sh

REPORT_DATE=$(date +"%Y-%m-%d")
REPORT_FILE="/var/log/memory_report_$REPORT_DATE.txt"

echo "======================================" > $REPORT_FILE
echo "容器内存使用报告 - $REPORT_DATE" >> $REPORT_FILE
echo "======================================" >> $REPORT_FILE
echo "" >> $REPORT_FILE

# 容器内存统计
echo "【容器内存使用情况】" >> $REPORT_FILE
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" >> $REPORT_FILE
echo "" >> $REPORT_FILE

# 系统内存统计
echo "【系统内存使用情况】" >> $REPORT_FILE
free -h >> $REPORT_FILE
echo "" >> $REPORT_FILE

# 告警汇总
echo "【今日告警汇总】" >> $REPORT_FILE
grep "$(date +"%Y-%m-%d")" /var/log/container_memory_monitor.log | wc -l >> $REPORT_FILE
echo "" >> $REPORT_FILE
```

### 维护建议

#### 日常维护任务
1. **每日检查**
   - 查看容器运行状态
   - 检查内存使用趋势
   - 查看应用日志异常

2. **每周维护**  
   - 清理无用的Docker镜像和容器
   - 检查磁盘空间使用
   - 更新监控脚本

3. **每月评估**
   - 分析内存使用报告
   - 评估是否需要进一步优化
   - 更新优化参数配置

#### 性能调优建议
1. **内存不足时**
   - 检查是否有内存泄露
   - 考虑增加-Xmx参数
   - 优化应用代码

2. **GC频繁时**
   - 调整年轻代大小(-Xmn)
   - 优化GC参数
   - 考虑更换垃圾收集器

3. **启动缓慢时**
   - 检查类加载情况
   - 优化Spring Boot配置
   - 考虑减少不必要的依赖

## 故障排除

### 常见问题及解决方案

#### 1. 容器启动失败

**症状：** 容器无法启动或立即退出

**排查步骤：**
```bash
# 查看容器状态
docker ps -a

# 查看启动日志
docker logs {CONTAINER_NAME}

# 检查镜像是否存在
docker images | grep {IMAGE_NAME}
```

**常见原因及解决：**
- **内存参数过小：** 增加-Xmx值
- **网络配置错误：** 检查--network参数
- **卷挂载问题：** 检查路径是否存在
- **环境变量错误：** 检查COMMAND_NAME格式

#### 2. 内存使用异常高

**症状：** 优化后内存使用仍然很高

**排查步骤：**
```bash
# 检查JVM内存配置是否生效
docker exec {CONTAINER_NAME} ps aux | grep java

# 查看实际堆内存限制
curl -s "http://localhost:PORT/actuator/metrics/jvm.memory.max?tag=area:heap"

# 检查是否有内存泄露
curl -s "http://localhost:PORT/actuator/heapdump" -o heapdump.hprof
```

**解决方案：**
- 检查JVM参数是否正确传递
- 分析堆内存转储文件
- 排查应用层面的内存泄露

#### 3. 应用性能下降

**症状：** 优化后响应变慢或出现超时

**排查步骤：**
```bash
# 检查GC情况
curl -s "http://localhost:PORT/actuator/metrics/jvm.gc.pause"

# 查看线程状态
curl -s "http://localhost:PORT/actuator/threaddump"

# 监控CPU使用率
docker stats {CONTAINER_NAME}
```

**解决方案：**
- 适当增加堆内存大小
- 调整GC参数
- 优化数据库连接池配置

#### 4. 数据库连接问题

**症状：** 优化后无法连接数据库

**排查步骤：**
```bash
# 检查网络连通性
docker exec {CONTAINER_NAME} ping mysql-prod

# 查看数据库连接配置
docker logs {CONTAINER_NAME} | grep -i "datasource\|mysql"

# 检查容器网络配置
docker inspect {CONTAINER_NAME} | grep NetworkMode
```

**解决方案：**
- 确认容器网络配置正确
- 检查数据库连接参数
- 验证数据库用户权限

### 回滚方案

#### 快速回滚步骤
```bash
# 1. 停止优化后的容器
docker stop {CONTAINER_NAME}
docker rm {CONTAINER_NAME}

# 2. 使用备份配置重新部署
# 从备份配置文件中提取原始参数
ORIGINAL_CONFIG=$(cat /tmp/{CONTAINER_NAME}-backup-config.json)

# 3. 重新创建原始容器
# 根据备份配置手动重建，或使用备份脚本

# 4. 验证功能正常
curl -s http://localhost:PORT/actuator/health
```

#### 回滚检查清单
- [ ] 应用功能正常
- [ ] 数据库连接正常
- [ ] 用户访问正常
- [ ] 定时任务运行正常
- [ ] 内存使用恢复到优化前水平

## 实践案例

### 成功案例总结

基于2025-07-25的实际优化经验：

#### 案例1：hyrm-imppc (yudao框架)
- **优化前：** 3.055GiB内存使用
- **优化后：** 1.06GiB内存使用
- **节省：** 1.995GiB (65.3%减少)
- **参数：** `-Xms512m -Xmx1024m -XX:+UseG1GC`
- **状态：** 运行稳定，功能正常

#### 案例2：hyrm-imusti (yudao框架)
- **优化前：** 2.86GiB内存使用
- **优化后：** 884.4MiB内存使用  
- **节省：** 1.976GiB (69.7%减少)
- **参数：** `-Xms512m -Xmx1024m -XX:+UseG1GC`
- **状态：** 运行稳定，功能正常

#### 案例3：ruoyi-crm (ruoyi框架)
- **优化前：** 1.491GiB内存使用
- **优化后：** 550.2MiB内存使用
- **节省：** 0.941GiB (63.1%减少)  
- **参数：** `-Xms256m -Xmx768m -XX:+UseG1GC`
- **状态：** 运行稳定，功能正常

### 优化效果总结
- **总节省内存：** 4.912GiB
- **平均优化幅度：** 66%
- **成功率：** 3/4 (75%)
- **无功能影响：** 所有成功优化的容器功能完全正常

---

## 文档版本历史

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| v1.0 | 2025-07-25 | 初始版本，基于实际优化经验创建 | 系统管理员 |

## 附录

### 参考资料
- [Oracle JVM参数官方文档](https://docs.oracle.com/javase/8/docs/technotes/tools/unix/java.html)
- [G1GC调优指南](https://docs.oracle.com/javase/8/docs/technotes/guides/vm/gctuning/g1_gc.html)
- [Spring Boot Actuator监控](https://docs.spring.io/spring-boot/docs/current/reference/html/actuator.html)
- [Docker容器资源限制](https://docs.docker.com/config/containers/resource_constraints/)

### 联系方式
如有问题或建议，请联系系统管理员或在项目Issues中提出。
