# 掌上青城(zsgj)业务运维手册

## 项目概览

**项目名称**: 掌上青城 (zsgj)
**技术架构**: PHP 5.6 + ThinkPHP 3.x + MySQL + Redis + Memcached
**服务器环境**: Ubuntu 24.04 + Docker容器化部署
**域名**: zsqc.51zsqc.com

## 服务架构

```
外部访问 → 雷池WAF → 1Panel OpenResty → PHP容器
                                      ↓
                        MySQL + Redis + Memcached 容器
```

### 核心容器服务

| 容器名称 | 镜像 | 端口映射 | 用途 |
|---------|------|----------|------|
| php-web-zsgj | zhao0829wang/apache2-php:5.6-rc2 | 8088 | zsgj主应用(PHP 5.6) |
| php-web-zsqc-apps | zhao0829wang/apache2-php:7.2-rc4 | 8089 | zsqc模块(PHP 7.2) |
| mysql-prod | mysql:5.7.44 | 3306 | 数据库服务 |
| redis-prod | redis:8.0.3 | 6379 | 缓存服务 |
| memcached-prod | memcached | 11211 | 缓存服务 |

## 目录结构

```
/mnt/datadisk0/
├── apps/php-web/zsqc/           # 应用配置
│   ├── docker-compose-zsgj.yml      # zsgj容器配置
│   ├── docker-compose-zsqc-apps.yml # zsqc模块容器配置
│   └── .env                          # 环境变量
├── volumns/php-web/zsqc/        # 应用数据
│   ├── zsgj/                         # zsgj主应用代码
│   └── zsqc-admin/                   # zsqc-admin后台代码
└── backups/                     # 备份文件
```

## 数据库信息

### 主要数据库

| 数据库名 | 用途 | 大小 | 备注 |
|---------|------|------|------|
| zsgj | zsgj主应用数据 | ~2.1GB | 核心业务数据 |
| zsqc-admin | 后台管理系统 | ~1.3GB | 管理员、配置等 |
| zsqc_cal_data | 计算数据 | ~3.2GB | 公交路线计算 |
| zsqc_garbage | 垃圾分类 | ~152MB | 垃圾分类功能 |
| zsqc_subway | 地铁信息 | ~2.6MB | 地铁相关数据 |

### 数据库连接信息
- **主机**: mysql-prod (容器内网络)
- **用户**: zsqc
- **密码**: Zsqc@2025#Secure
- **端口**: 3306

## 日常维护操作

### 1. 服务状态检查

```bash
# 检查所有容器状态
docker ps | grep -E "(php-web|mysql-prod|redis-prod|memcached-prod)"

# 检查容器资源使用
docker stats --no-stream

# 检查服务端口
netstat -tlnp | grep -E "(8088|8089|3306|6379|11211)"
```

### 2. 应用日志查看

#### 容器应用日志
```bash
# PHP应用日志 (zsgj)
docker exec html-51zsqc-zsgj tail -f /var/log/apache2/error.log

# PHP应用日志 (zsqc-admin) - 错误日志重定向到容器日志
docker logs php-web-zsqc-apps --tail 100 -f

# ThinkPHP运行日志
docker exec php-web-zsqc-apps tail -f /var/www/html/zsqc-admin/runtime/log/$(date +%Y%m)/$(date +%d).log

# MySQL错误日志
docker logs mysql-prod --tail 100

# Redis日志
docker logs redis-prod --tail 100
```

#### 1Panel网站访问日志
```bash
# 查看网站访问日志 (实时)
tail -f /opt/1panel/www/sites/zsqc.51zsqc.com/log/access.log

# 查看网站错误日志 (实时)
tail -f /opt/1panel/www/sites/zsqc.51zsqc.com/log/error.log

# 查看最近100条访问记录
tail -100 /opt/1panel/www/sites/zsqc.51zsqc.com/log/access.log

# 查看最近100条错误记录
tail -100 /opt/1panel/www/sites/zsqc.51zsqc.com/log/error.log

# 按日期筛选访问日志 (今天)
grep "$(date '+%d/%b/%Y')" /opt/1panel/www/sites/zsqc.51zsqc.com/log/access.log

# 统计今日访问量
grep "$(date '+%d/%b/%Y')" /opt/1panel/www/sites/zsqc.51zsqc.com/log/access.log | wc -l

# 查看访问最多的IP
awk '{print $1}' /opt/1panel/www/sites/zsqc.51zsqc.com/log/access.log | sort | uniq -c | sort -nr | head -10

# 查看访问最多的页面
awk '{print $7}' /opt/1panel/www/sites/zsqc.51zsqc.com/log/access.log | sort | uniq -c | sort -nr | head -10
```

#### 日志文件位置说明
- **1Panel访问日志**: `/opt/1panel/www/sites/zsqc.51zsqc.com/log/access.log`
- **1Panel错误日志**: `/opt/1panel/www/sites/zsqc.51zsqc.com/log/error.log`
- **日志格式**: nginx标准访问日志格式，包含IP、时间、请求方法、URL、状态码、响应大小等信息

### 3. 数据库维护

```bash

# 查看数据库大小
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' -e "
SELECT 
    table_schema as 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema IN ('zsgj', 'zsqc-admin', 'zsqc_cal_data', 'zsqc_garbage', 'zsqc_subway')
GROUP BY table_schema;"
```

### 4. 缓存管理

```bash
# Redis缓存状态
docker exec redis-prod redis-cli INFO memory

# 清理Redis缓存
docker exec redis-prod redis-cli FLUSHALL

# Memcached状态
docker exec memcached-prod echo "stats" | nc localhost 11211

# 重启缓存服务
docker restart redis-prod memcached-prod
```

### 5. 代码更新部署

```bash
# 进入代码目录
cd /mnt/datadisk0/volumns/php-web/zsqc

# 拉取最新代码 (zsgj)
cd zsgj && git pull origin master

# 拉取最新代码 (zsqc-admin)
cd ../zsqc-admin && git pull origin master

# 重启PHP容器使配置生效
docker restart php-web-zsgj php-web-zsqc-apps
```


## 更新记录

| 日期 | 版本 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 2025-07-29 | v1.0 | 初始版本创建 | Claude |

---
