---
date_created: 2025-07-29 16:03:21
date_modified: 2025-07-29 16:03:23
author: 赵王飞
---

# 背景
掌上青城迁移完成后，进行测试。zsqc-admin相关测试基本通过,maintain模块的业务使用了旧的写死的的IP等信息,暂时不予以修复。
下一步进行zsgj模块测试,测试使用 curl 进行请求,返回403.

```bash
curl -X POST "http://zsgj.51zsqc.com/zsgj/Service/Bus/getMultiReal" \
-H "User-Agent: Mozilla/5.0 (Linux; Android 10; V2001A Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0
Chrome/78.0.3904.96 Mobile Safari/537.36(com.unionpay.mobilepay) (cordova 7.0.0) (updebug 0) (clientVersion 321) (version 1021)(UnionPay/1.0
CloudPay)(language zh_CN)(languageFamily zh_CN)(upApplet single)(walletMode 00)" \
-H "Content-Type: application/json" \
-d '{"data":{"params":[{"line_id":291,"station_no":8},{"line_id":661,"station_no":17},{"line_id":12410,"station_no":28},{"line_id":290,"station_n     
o":16},{"line_id":660,"station_no":19},{"line_id":12411,"station_no":12}]}}'
```

```html
<html>
<head><title>403 Forbidden</title></head>
<body bgcolor="white">
<center><h1>403 Forbidden</h1></center>
<hr><center>nginx</center>
</body>
</html>
```
需要你查看容器日志等帮我确定问题。

## 问题排查过程

### 第一轮排查：403 Forbidden错误

**问题现象**：使用域名`zsgj.51zsqc.com`访问API时返回403错误

**排查步骤**：
1. **DNS解析检查**：`nslookup zsgj.51zsqc.com` - 解析正常
2. **雷池WAF配置检查**：
   ```bash
   docker exec safeline-tengine grep -r "zsgj.51zsqc.com" /etc/nginx/sites-enabled/
   # 结果：未找到zsgj.51zsqc.com配置
   
   docker exec safeline-tengine grep -r "zsqc.51zsqc.com" /etc/nginx/sites-enabled/
   # 结果：找到zsqc.51zsqc.com在backend_8中配置
   ```

**问题根因**：域名配置错误，应该使用`zsqc.51zsqc.com`而不是`zsgj.51zsqc.com`

### 第二轮排查：400/404错误

修正域名后出现新问题：

**测试命令**：
```bash
curl -X POST "http://zsqc.51zsqc.com/zsgj/Service/Bus/getMultiReal" \
-H "Referer: https://unionpay.nmzyb.cn/zsqc-ysf/index.html" \
-H "User-Agent: Test(com.unionpay.mobilepay)(UnionPay/1.0 CloudPay)" \
-H "Content-Type: application/json" \
-d '{"data":{"params":[{"line_id":291,"station_no":8}]}}'
```

**错误现象**：404 Not Found - API路径不存在

**排查发现**：
1. **容器端口映射问题**：
   ```bash
   docker ps | grep -E "(8088|8089)"
   # zsgj容器在8088端口，zsqc-apps容器在8089端口
   ```

2. **1Panel代理配置问题**：
   ```bash
   cat /opt/1panel/www/sites/zsqc.51zsqc.com/proxy/root.conf
   # 发现代理指向8089端口（zsqc-apps），而不是8088端口（zsgj）
   ```

3. **zsgj容器500错误**：
   ```bash
   curl -I "http://127.0.0.1:8088/"
   # HTTP/1.0 500 Internal Server Error
   
   docker exec html-51zsqc-zsgj cat /var/log/apache2/error.log | tail -10
   # PHP Fatal error: Class 'Cache' not found
   ```

### 第三轮排查：深度问题分析

**发现的核心问题**：

1. **PHP依赖缺失**：
   - zsgj容器（8088端口）存在`PHP Fatal error: Class 'Cache' not found`
   - 导致所有请求返回500内部错误

2. **代理配置错误**：
   - 1Panel配置将`zsqc.51zsqc.com`代理到8089端口（zsqc-apps容器）
   - 而zsgj的Bus API在8088端口（zsgj容器）
   - 请求被发送到了错误的容器

3. **API路径格式错误**：
   - 错误路径：`/zsgj/Service/Bus/getMultiReal`
   - 正确路径：`/index.php/Service/Bus/getMultiReal`（ThinkPHP 3.x格式）

4. **云闪付渠道检测**：
   - 代码要求同时满足Referer和User-Agent条件
   - 之前测试缺少Referer头导致返回"服务暂停"消息

## 问题总结

### 主要技术问题
1. **容器PHP环境**：zsgj容器存在Cache类缺失的致命错误
2. **网络代理配置**：1Panel反向代理指向错误的容器端口
3. **API路径格式**：ThinkPHP框架需要包含index.php的完整路径

### 配置问题
1. **域名映射**：测试使用了未配置的域名
2. **端口映射**：代理配置与实际容器端口不匹配

### 业务逻辑问题
1. **渠道控制**：云闪付渠道检测需要完整的请求头信息

## 修复方案

### 立即修复项
1. **修复zsgj容器PHP错误**：
   ```bash
   # 需要修复Cache类依赖或重新部署容器
   ```

2. **修正1Panel代理配置**：
   ```bash
   # 将zsqc.51zsqc.com的代理从8089改为8088端口
   ```

### 正确的测试命令

**Git Bash版本**：
```bash
curl -X POST "http://zsqc.51zsqc.com/index.php/Service/Bus/getMultiReal" \
-H "Referer: https://unionpay.nmzyb.cn/zsqc-ysf/index.html" \
-H "User-Agent: Test(com.unionpay.mobilepay)(UnionPay/1.0 CloudPay)" \
-H "Content-Type: application/json" \
-d '{"data":{"params":[{"line_id":291,"station_no":8}]}}'
```

**PowerShell版本（使用原生curl.exe）**：
```powershell
curl.exe -X POST "http://zsqc.51zsqc.com/index.php/Service/Bus/getMultiReal" -H "Referer: https://unionpay.nmzyb.cn/zsqc-ysf/index.html" -H "User-Agent: Test(com.unionpay.mobilepay)(UnionPay/1.0 CloudPay)" -H "Content-Type: application/json" -d "{\"data\":{\"params\":[{\"line_id\":291,\"station_no\":8}]}}"
```

**PowerShell版本（使用Invoke-WebRequest）**：
```powershell
$headers = @{
    "Referer" = "https://unionpay.nmzyb.cn/zsqc-ysf/index.html"
    "User-Agent" = "Test(com.unionpay.mobilepay)(UnionPay/1.0 CloudPay)"
    "Content-Type" = "application/json"
}
$body = '{"data":{"params":[{"line_id":291,"station_no":8}]}}'
Invoke-WebRequest -Uri "http://zsqc.51zsqc.com/index.php/Service/Bus/getMultiReal" -Method POST -Headers $headers -Body $body
```

## 当前状态

**迁移状态**：部分成功
- ✅ 网络层面配置正常
- ✅ 容器启动正常  
- ❌ 应用层面存在PHP依赖问题
- ❌ 代理配置需要修正

**下一步操作**：
1. 修复zsgj容器的PHP Cache类错误
2. 修正1Panel代理配置指向正确端口
3. 使用修正后的API路径重新测试
4. 验证云闪付渠道检测功能


https://unionpay.nmzyb.cn/zsqc-admin/public/index.php/app/bus_line/getNearbyStation?lat=40.835578&lng=111.721482

https://zsqc.51zsqc.com/zsqc-admin/public/index.php/app/bus_line/getNearbyStation?lat=40.835578&lng=111.721482