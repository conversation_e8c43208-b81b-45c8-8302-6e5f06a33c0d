# OpenVPN管理手册

## 概述

本文档提供腾讯云服务器OpenVPN日常管理操作指南。OpenVPN服务运行在Docker容器中，使用kylemanna/openvpn镜像，无并发连接数限制。

## 服务信息

### 基本配置
- **容器名称**: `1Panel-openvpn-sNji`
- **镜像**: `kylemanna/openvpn:latest`
- **网络**: `1panel-network` (172.20.0.0/16)
- **VPN网段**: `172.27.224.0/24`
- **端口**: `1194/udp`
- **配置目录**: `/opt/1panel/apps/openvpn/openvpn/data`

### 网络架构
```
外部客户端 → VPN服务器(1194/udp) → 1panel-network(172.20.0.0/16) → 容器服务
```

## 管理脚本使用

### 脚本位置
```bash
/opt/1panel/apps/openvpn/openvpn/ovpn-manager.sh
```

### 基本命令

#### 1. 查看帮助
```bash
/opt/1panel/apps/openvpn/openvpn/ovpn-manager.sh help
```

#### 2. 列出所有用户
```bash
/opt/1panel/apps/openvpn/openvpn/ovpn-manager.sh list
```

#### 3. 创建新用户
```bash
/opt/1panel/apps/openvpn/openvpn/ovpn-manager.sh create [用户名]
```
- 自动生成证书和配置文件
- 配置文件保存在 `/tmp/tencent-[用户名].ovpn`

#### 4. 撤销用户
```bash
/opt/1panel/apps/openvpn/openvpn/ovpn-manager.sh revoke [用户名]
```
- 自动撤销证书、更新CRL、重启服务

#### 5. 检查服务状态
```bash
/opt/1panel/apps/openvpn/openvpn/ovpn-manager.sh status
```

#### 6. 批量创建用户
```bash
# 创建用户列表文件
echo -e "zhangsan\nlisi\nwangwu" > users.txt
/opt/1panel/apps/openvpn/openvpn/ovpn-manager.sh batch-create users.txt
```

#### 7. 清理临时文件
```bash
/opt/1panel/apps/openvpn/openvpn/ovpn-manager.sh cleanup
```

## 手动操作

### 容器管理
```bash
# 查看容器状态
docker ps | grep 1Panel-openvpn-sNji

# 查看容器日志
docker logs 1Panel-openvpn-sNji --tail 20

# 重启容器
docker restart 1Panel-openvpn-sNji

# 进入容器
docker exec -it 1Panel-openvpn-sNji bash
```

### 用户证书管理
```bash
cd /opt/1panel/apps/openvpn/openvpn

# 生成用户证书
docker run -v $(pwd)/data:/etc/openvpn --rm kylemanna/openvpn easyrsa build-client-full [用户名] nopass

# 生成配置文件
docker run -v $(pwd)/data:/etc/openvpn --rm kylemanna/openvpn ovpn_getclient [用户名] > [用户名].ovpn

# 撤销用户证书
echo "yes" | docker run -v $(pwd)/data:/etc/openvpn --rm -i kylemanna/openvpn easyrsa revoke [用户名]

# 更新撤销列表
docker run -v $(pwd)/data:/etc/openvpn --rm kylemanna/openvpn easyrsa gen-crl

# 查看用户列表
docker run -v $(pwd)/data:/etc/openvpn --rm kylemanna/openvpn ovpn_listclients
```

## 配置文件说明

### 客户端配置文件格式
生成的配置文件命名格式：`tencent-[用户名].ovpn`

### 配置文件内容
```
client
nobind
dev tun
remote-cert-tls server
remote 111.229.155.148 1194 udp

<key>
[客户端私钥]
</key>

<cert>
[客户端证书]
</cert>

<ca>
[CA证书]
</ca>

<tls-auth>
[TLS认证密钥]
</tls-auth>
```

### 配置文件使用注意事项
- 每个用户必须使用独立的配置文件
- 配置文件包含敏感信息，需要安全传输
- 不能多人共用一个配置文件
- 配置文件泄露后需要立即撤销对应证书

## 监控与维护

### 服务状态检查
```bash
# 检查端口监听
netstat -tulpn | grep 1194

# 检查容器网络
docker inspect 1Panel-openvpn-sNji | grep IPAddress

# 检查路由配置
docker exec 1Panel-openvpn-sNji ip route
```

### 日志监控
```bash
# 实时查看连接日志
docker logs -f 1Panel-openvpn-sNji

# 查看最近错误
docker logs 1Panel-openvpn-sNji --tail 50 | grep -i error

# 查看连接统计
docker exec 1Panel-openvpn-sNji cat /tmp/openvpn_status.log
```

### 性能监控
```bash
# 查看容器资源使用
docker stats 1Panel-openvpn-sNji

# 查看网络流量
docker exec 1Panel-openvpn-sNji cat /proc/net/dev
```

## 故障排除

### 常见问题

#### 1. 客户端连接失败
**症状**: 客户端显示连接超时或认证失败

**排查步骤**:
```bash
# 检查容器状态
docker ps | grep 1Panel-openvpn-sNji

# 检查端口监听
netstat -tulpn | grep 1194

# 查看连接日志
docker logs 1Panel-openvpn-sNji --tail 20
```

#### 2. 证书认证错误
**症状**: 日志显示"authentication failed"

**解决方案**:
```bash
# 检查用户证书状态
/opt/1panel/apps/openvpn/openvpn/ovpn-manager.sh list

# 重新生成用户配置
/opt/1panel/apps/openvpn/openvpn/ovpn-manager.sh create [用户名]
```

#### 3. 网络路由问题
**症状**: VPN连接成功但无法访问内网服务

**排查步骤**:
```bash
# 检查容器路由
docker exec 1Panel-openvpn-sNji ip route

# 检查1panel-network连通性
docker exec 1Panel-openvpn-sNji ping **********
```

#### 4. 压缩设置冲突错误
**症状**: 客户端连接时出现以下错误
```
Compression or compression stub framing is not allowed since data-channel offloading is enabled.
OPTIONS ERROR: server pushed compression settings that are not allowed and will result in a non-working connection.
ERROR: Failed to apply push options
Failed to open tun/tap interface
```


#### 9. 连接超时自动重连
**症状**: 连接正常使用但定期出现重连
```
Inactivity timeout (--ping-restart), restarting
SIGUSR1[soft,ping-restart] received, process restarting
```

**原因**: 
- 网络间歇性不稳定
- 服务器端keepalive设置较短
- 客户端网络环境变化（如WiFi切换）

**解决方案**: 
1. **调整keepalive设置**:
   ```
   # 增加keepalive间隔，减少误判
   keepalive 10 60
   ping-restart 180
   ```

2. **网络环境优化**:
   - 使用稳定的网络连接
   - 避免频繁切换网络环境
   - 检查防火墙是否阻断keepalive包

**注意**: 这种重连是正常的容错机制，不会丢失数据，只是会有短暂的连接中断。

### 紧急操作

#### 服务重启
```bash
docker restart 1Panel-openvpn-sNji
```

#### 回滚到Access Server
```bash
cd /opt/1panel/apps/openvpn/openvpn
docker stop 1Panel-openvpn-sNji
cp docker-compose.yml.backup docker-compose.yml
docker compose up -d
```

## 安全建议

### 用户管理
- 定期审查用户列表，删除不需要的用户
- 为每个用户创建独立的证书
- 及时撤销离职人员的证书

### 配置文件管理
- 使用安全渠道分发配置文件
- 定期更换CA证书（每2-3年）
- 监控异常连接活动

### 网络安全
- 限制VPN访问的内网范围
- 配置防火墙规则
- 定期更新OpenVPN镜像

## 备份与恢复

### 备份重要数据
```bash
# 备份整个配置目录
cp -r /opt/1panel/apps/openvpn /opt/1panel/apps/openvpn-backup-$(date +%Y%m%d)

# 备份PKI证书
docker run -v /opt/1panel/apps/openvpn/openvpn/data:/etc/openvpn --rm kylemanna/openvpn tar czf /etc/openvpn/pki-backup-$(date +%Y%m%d).tar.gz /etc/openvpn/pki/
```

### 恢复操作
```bash
# 恢复配置目录
cp -r /opt/1panel/apps/openvpn-backup-[日期] /opt/1panel/apps/openvpn

# 重启服务
docker restart 1Panel-openvpn-sNji
```

## 路由配置优化

### 问题描述
默认的OpenVPN配置会导致所有流量通过VPN隧道，包括DNS查询被阻塞，影响正常的网络访问。

### 解决方案
修改客户端配置文件，实现分流路由：

#### 配置文件修改步骤
1. **删除全流量重定向**：
   ```
   # 删除这行
   redirect-gateway def1
   ```

2. **添加精确路由**：
   ```
   # 只路由腾讯云相关流量
   route 172.20.0.0 255.255.0.0      # 1Panel容器网络
   route 172.27.224.0 255.255.240.0  # VPN客户端网段
   
   # 防止DNS被阻塞
   pull-filter ignore "block-outside-dns"
   ```



### 修改示例
```bash

# 修改前的配置文件包含
redirect-gateway def1
# 用来配置路由，直接访问腾讯云服务器局域网段
route 172.17.0.0 255.255.0.0
# 用来配置路由，直接访问这 docker 容器局域网段
route 172.20.0.0 255.255.0.0
route 172.27.224.0 255.255.240.0
pull-filter ignore "block-outside-dns"
```

## 当前用户清单

### 活跃用户
- zwf (tencent-zwf.ovpn) - 已优化路由配置
- jjb (tencent-jjb.ovpn)
- wj (tencent-wj.ovpn)
- zxj (tencent-zxj.ovpn)
- xhp (tencent-xhp.ovpn)

### 已撤销用户
- user1 (测试用户，已撤销)
- user2 (测试用户，已撤销)
- user3 (测试用户，已撤销)
- admin (测试用户，已撤销)

---

**注意**: 本文档为常态化管理文档，不包含迁移过程记录。如需查看迁移详情，请参考相应的迁移计划文档。