---
date_created: 2025-07-28 13:53:23
date_modified: 2025-07-28 13:53:24
author: 赵王飞
---
# 背景

之前我们配置的 open VPN 在登录时，需要输入用户名、密码[OpenVPN Access Server客户端配置执行记录-hy-tencent.md], 配置完成后，在多个用户同时登录时, 一个登用户登录以后,后续就好像就会提示:错误的凭证请重试.

## 错误日志
```
2025-07-28 13:52:14 Validating certificate extended key usage
2025-07-28 13:52:14 ++ Certificate has EKU (str) TLS Web Server Authentication, expects TLS Web Server Authentication
2025-07-28 13:52:14 VERIFY EKU OK
2025-07-28 13:52:14 VERIFY OK: depth=0, CN=OpenVPN Server
2025-07-28 13:52:14 Control Channel: TLSv1.3, cipher TLSv1.3 TLS_AES_256_GCM_SHA384, peer certificate: 384 bit EC, curve secp384r1, signature: ecdsa-with-SHA256
2025-07-28 13:52:14 [OpenVPN Server] Peer Connection Initiated with [AF_INET]***************:1194
2025-07-28 13:52:14 AUTH: Received control message: AUTH_FAILED,LICENSE: Access Server license failure: maximum concurrent_connections exceeded (2)
2025-07-28 13:52:14 SIGUSR1[soft,auth-failure] received, process restarting
2025-07-28 13:52:14 MANAGEMENT: >STATE:**********,RECONNECTING,auth-failure,,,,,
2025-07-28 13:52:14 Restart pause, 5 second(s)
```

## 问题原因分析

**根本原因：OpenVPN Access Server许可证并发连接数限制**

通过日志分析，问题的关键在于第16行的错误信息：
```
AUTH_FAILED,LICENSE: Access Server license failure: maximum concurrent_connections exceeded (2)
```

### 问题详解
1. **许可证限制**：当前OpenVPN Access Server设置了最大并发连接数为2个
2. **误导性错误**：当超过连接数限制时，系统返回`AUTH_FAILED`认证失败错误
3. **用户体验问题**：客户端收到认证失败消息，显示为"错误的凭据请重试"
4. **实际情况**：用户凭据是正确的，问题出在许可证连接数超限

### 影响范围
- 同时在线用户数不能超过2个
- 第3个及以后的用户会遇到"凭据错误"提示
- 已连接用户断开后，新用户才能成功连接

## 当前环境分析

### Docker容器化架构
- **容器名称**: `1Panel-openvpn-sNji`
- **镜像**: `openvpn/openvpn-as:latest`
- **网络模式**: 连接到`1panel-network`(**********/16)
- **容器IP**: **********
- **数据目录**: `/opt/1panel/apps/openvpn/openvpn/data` 挂载到容器`/openvpn`

### 端口映射配置
- **UDP 1194**: VPN连接端口 (0.0.0.0:1194->1194/udp)
- **TCP 943**: 管理界面 (0.0.0.0:943->943/tcp)  
- **TCP 7443**: HTTPS管理界面 (0.0.0.0:7443->443/tcp)

### VPN网络配置
OpenVPN Access Server自动创建了16个VPN子网：
- ************/24 - ************/24
- 每个子网对应不同的VPN实例/用户组
- VPN客户端分配到这些子网中的IP

### 1Panel网络集成
- **主网络**: `1panel-network` (bridge模式)
- **网络范围**: **********/16
- **网关**: **********
- **容器互通**: 容器通过名称进行内部通信
- **外部访问**: 通过端口映射直接暴露到宿主机

### 关键发现
1. **容器化运行**: OpenVPN运行在Docker容器中，不是宿主机直接安装
2. **网络隔离**: VPN网络(172.27.x.x)与容器网络(172.20.x.x)分离
3. **路由配置**: 容器内自动配置了VPN网络路由
4. **许可证限制**: 确认为Access Server的2连接限制问题


